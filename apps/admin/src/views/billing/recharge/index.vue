<script setup lang="ts">
import type { StdCurdConfig } from '@billing/curd'
import type { RechargeRecord, RechargeStatsResponse, UserBalanceInfo } from '@/api/recharge'
import { StdCurd } from '@billing/curd'
import { Alert, AlertDescription, Avatar, AvatarFallback, AvatarImage, Badge, Card, CardContent, CardHeader, CardTitle, Tabs, TabsContent, TabsList, TabsTrigger } from '@billing/ui'
import { CheckCircle, CreditCard, DollarSign, Info, TrendingUp, Wallet, XCircle } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { searchUsers } from '@/api'
import { rechargeApi } from '@/api/recharge'
import { FORM_OPTIONS, RECHARGE_TYPE_LABELS } from '@/constants'
import { formatCurrency, formatDate } from '@/utils/format'

// 状态定义
const activeTab = ref('list')

// 统计数据
const stats = ref<RechargeStatsResponse>({
  today_amount: 0,
  month_amount: 0,
  total_count: 0,
  average_amount: 0,
  pending_amount: 0,
  completed_amount: 0,
  failed_amount: 0,
  total_users: 0,
  active_users: 0,
  total_balance: 0,
})

// 用户余额数据
const userBalances = ref<UserBalanceInfo[]>([])

// 趋势数据
const rechargeTrackData = ref([
  { date: '2024-01-01', amount: 1200, count: 8 },
  { date: '2024-01-02', amount: 1850, count: 12 },
  { date: '2024-01-03', amount: 980, count: 6 },
  { date: '2024-01-04', amount: 2340, count: 15 },
  { date: '2024-01-05', amount: 1650, count: 11 },
])

// StdCurd 配置
const config: StdCurdConfig<RechargeRecord> = {
  api: rechargeApi,
  title: '充值管理',
  primaryKey: 'id',

  // 表单字段配置
  formFields: [
    {
      key: 'user_id',
      label: '关联用户',
      type: 'combobox',
      required: true,
      placeholder: '请输入用户名或邮箱搜索',
      remoteSearch: {
        searchApi: searchUsers,
        debounceMs: 300,
        minSearchLength: 1,
        showDefaultOptions: false,
        renderOption: (option) => {
          const parts = [option.label]
          if (option.email) {
            parts.push(`(${option.email})`)
          }
          return parts.join(' ')
        },
      },
    },
    {
      key: 'amount',
      label: '充值金额',
      type: 'number',
      required: true,
      rules: [
        { required: true, message: '充值金额是必填项' },
        { min: 0.01, message: '充值金额必须大于0' },
      ],
    },
    {
      key: 'type',
      label: '充值类型',
      type: 'select',
      defaultValue: 'admin',
      options: FORM_OPTIONS.RECHARGE_TYPES,
    },
    {
      key: 'description',
      label: '备注',
      type: 'textarea',
      placeholder: '请输入充值备注...',
    },
  ],

  // 功能开关
  features: {
    create: true,
    edit: false,
    delete: false,
    batchDelete: false,
    search: true,
  },

  btnText: {
    create: '充值',
  },

  // 消息配置
  successMessages: {
    create: '充值成功！',
  },
}

// 计算属性：充值趋势
const rechargeTrend = computed(() => {
  if (rechargeTrackData.value.length < 2)
    return { direction: 'stable', percentage: 0 }

  const current = rechargeTrackData.value[rechargeTrackData.value.length - 1]
  const previous = rechargeTrackData.value[rechargeTrackData.value.length - 2]

  const change = ((current.amount - previous.amount) / previous.amount) * 100

  return {
    direction: change > 0 ? 'up' : change < 0 ? 'down' : 'stable',
    percentage: Math.abs(change).toFixed(1),
  }
})

// 获取统计数据
async function fetchStats() {
  try {
    const response = await rechargeApi.getStats()
    stats.value = response
  }
  catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取用户余额数据
async function fetchUserBalances() {
  try {
    const response = await rechargeApi.getUserBalances()
    userBalances.value = response
  }
  catch (error) {
    console.error('获取用户余额数据失败:', error)
  }
}

// 获取状态颜色
function getStatusVariant(status: string) {
  switch (status) {
    case 'completed':
      return 'default'
    case 'pending':
      return 'secondary'
    case 'failed':
      return 'destructive'
    case 'cancelled':
      return 'outline'
    default:
      return 'secondary'
  }
}

// 获取状态文本
function getStatusText(status: string) {
  const statusMap = {
    completed: '已完成',
    pending: '待处理',
    failed: '失败',
    cancelled: '已取消',
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const totalAmount = computed(() => {
  return stats.value.completed_amount + stats.value.failed_amount + stats.value.pending_amount
})

// 初始化
onMounted(() => {
  fetchStats()
  fetchUserBalances()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          充值管理
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          管理用户充值记录，为用户账户增加余额。支持多种充值方式和批量操作。
        </p>
      </div>
    </div>

    <!-- 计费方式说明 -->
    <Alert>
      <Info class="w-4 h-4" />
      <AlertDescription>
        <strong>充值说明：</strong>用户余额用于在资源包不足时进行计费。管理员可以直接为用户充值，用户也可以通过第三方支付完成充值。
      </AlertDescription>
    </Alert>

    <!-- 标签页导航 -->
    <Tabs
      v-model="activeTab"
      class="w-full"
    >
      <TabsList class="grid w-full grid-cols-3 mb-2">
        <TabsTrigger value="overview">
          概览
        </TabsTrigger>
        <TabsTrigger value="list">
          充值记录
        </TabsTrigger>
        <TabsTrigger value="analytics">
          趋势分析
        </TabsTrigger>
      </TabsList>

      <!-- 概览标签页 -->
      <TabsContent
        value="overview"
        class="space-y-6"
      >
        <!-- 核心统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                今日充值
              </CardTitle>
              <DollarSign class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ formatCurrency(stats.today_amount) }}
              </div>
              <p class="text-xs text-muted-foreground">
                <span :class="rechargeTrend.direction === 'up' ? 'text-green-600' : rechargeTrend.direction === 'down' ? 'text-red-600' : 'text-gray-600'">
                  {{ rechargeTrend.direction === 'up' ? '+' : rechargeTrend.direction === 'down' ? '-' : '' }}{{ rechargeTrend.percentage }}%
                </span>
                与昨日对比
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                本月充值
              </CardTitle>
              <TrendingUp class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ formatCurrency(stats.month_amount) }}
              </div>
              <p class="text-xs text-muted-foreground">
                {{ stats.total_count }} 次充值
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                平均充值
              </CardTitle>
              <CreditCard class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ formatCurrency(stats.average_amount) }}
              </div>
              <p class="text-xs text-muted-foreground">
                单次平均金额
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                用户总余额
              </CardTitle>
              <Wallet class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ formatCurrency(stats.total_balance) }}
              </div>
              <p class="text-xs text-muted-foreground">
                {{ stats.active_users }}/{{ stats.total_users }} 活跃用户
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 详细统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 充值状态分布 -->
          <Card>
            <CardHeader>
              <CardTitle>充值状态分布</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-green-500 rounded-full" />
                  <span class="text-sm">已完成</span>
                </div>
                <div class="text-right">
                  <div class="font-medium">
                    {{ formatCurrency(stats.completed_amount) }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ Math.round((stats.completed_amount / totalAmount) * 100) }}%
                  </div>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-yellow-500 rounded-full" />
                  <span class="text-sm">待处理</span>
                </div>
                <div class="text-right">
                  <div class="font-medium">
                    {{ formatCurrency(stats.pending_amount) }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ Math.round((stats.pending_amount / totalAmount) * 100) }}%
                  </div>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-red-500 rounded-full" />
                  <span class="text-sm">失败</span>
                </div>
                <div class="text-right">
                  <div class="font-medium">
                    {{ formatCurrency(stats.failed_amount) }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ Math.round((stats.failed_amount / totalAmount) * 100) }}%
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 用户余额排行 -->
          <Card>
            <CardHeader>
              <CardTitle>用户余额排行</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-3">
                <div
                  v-for="(user, index) in userBalances.slice(0, 5)"
                  :key="user.id"
                  class="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div class="flex items-center gap-3">
                    <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center text-xs font-medium">
                      {{ index + 1 }}
                    </div>
                    <Avatar class="h-8 w-8">
                      <AvatarFallback>{{ user.name.charAt(0) }}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div class="text-sm font-medium">
                        {{ user.name }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ user.email }}
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">
                      {{ formatCurrency(user.balance) }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ user.last_recharge || '暂无充值' }}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <!-- 充值记录标签页 -->
      <TabsContent
        value="list"
        class="space-y-6"
      >
        <!-- 使用 StdCurd 组件 -->
        <StdCurd :config="config">
          <!-- 自定义用户列渲染 -->
          <template #cell-user_id="{ row }">
            <div
              v-if="row.user"
              class="flex items-center"
            >
              <Avatar class="h-8 w-8 mr-3">
                <AvatarImage :src="row.user.avatar" />
                <AvatarFallback>{{ (row.user.name || '').charAt(0).toUpperCase() }}</AvatarFallback>
              </Avatar>
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ row.user.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ row.user.email }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="text-sm text-gray-400"
            >
              用户信息缺失
            </div>
          </template>

          <!-- 自定义金额列渲染 -->
          <template #cell-amount="{ value }">
            <div class="text-sm font-medium text-green-600">
              {{ formatCurrency(value) }}
            </div>
          </template>

          <!-- 自定义类型列渲染 -->
          <template #cell-type="{ value }">
            <Badge variant="outline">
              {{ RECHARGE_TYPE_LABELS[value] }}
            </Badge>
          </template>

          <!-- 自定义状态列渲染 -->
          <template #cell-status="{ value }">
            <Badge :variant="getStatusVariant(value)">
              <CheckCircle
                v-if="value === 'completed'"
                class="w-3 h-3 mr-1"
              />
              <XCircle
                v-else-if="value === 'failed'"
                class="w-3 h-3 mr-1"
              />
              {{ getStatusText(value) }}
            </Badge>
          </template>

          <!-- 自定义操作员列渲染 -->
          <template #cell-operator="{ row }">
            <div
              v-if="row.operator"
              class="text-sm text-gray-600"
            >
              {{ row.operator.name }}
            </div>
            <div
              v-else
              class="text-sm text-gray-400"
            >
              系统
            </div>
          </template>

          <!-- 自定义创建时间列渲染 -->
          <template #cell-created_at="{ value }">
            <div class="text-sm text-gray-500">
              {{ formatDate(value) }}
            </div>
          </template>
        </StdCurd>
      </TabsContent>

      <!-- 趋势分析标签页 -->
      <TabsContent
        value="analytics"
        class="space-y-6"
      >
        <Card>
          <CardHeader>
            <CardTitle>趋势分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-center py-8 text-gray-500">
              <TrendingUp class="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>趋势分析功能开发中...</p>
              <p class="text-sm">
                将提供充值趋势图表、用户行为分析等功能
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
