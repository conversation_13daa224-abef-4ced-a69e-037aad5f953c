<script setup lang="ts">
import type { CurdConfig } from '@billing/curd'

import type { Api<PERSON><PERSON> } from '@/api'
import { StdCurd } from '@billing/curd'
import {
  Alert,
  AlertDescription,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@billing/ui'
import { Activity, CheckCheck, Copy, Eye, Info, Key, Package, ShieldCheck, ShieldX, Wallet } from 'lucide-vue-next'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { keyApi, searchUsers } from '@/api'
import { useCopy } from '@/composables/useCopy'
import {
  BILLING_DESCRIPTIONS,
  CONFIRM_MESSAGES,
  FORM_OPTIONS,
  PAGINATION,
  SUCCESS_MESSAGES,
} from '@/constants'
import { API_KEY_STATUS } from '@/constants/common'
import { formatCurrency, formatDate, maskApiKey } from '@/utils'

const router = useRouter()
const activeTab = ref('list')
const { copyApiKey, isCopied } = useCopy()

// 统计数据
const stats = ref({
  totalKeys: 0,
  activeKeys: 0,
  blockedKeys: 0,
  keysWithQuota: 0,
  keysWithBalance: 0,
  totalQuotaUsage: 0,
  avgQuotaUsage: 0,
})

// 获取统计数据
async function fetchStats() {
  try {
    const response = await keyApi.getOverviewStats()
    stats.value = {
      totalKeys: response.total_keys,
      activeKeys: response.active_keys,
      blockedKeys: response.blocked_keys,
      keysWithQuota: response.keys_with_quota,
      keysWithBalance: response.keys_with_balance,
      totalQuotaUsage: response.total_quota_usage,
      avgQuotaUsage: response.avg_quota_usage,
    }

    // 同时更新模块统计数据
    moduleStats.value = response.module_stats.map(stat => ({
      module: stat.module,
      name: stat.name,
      keyCount: stat.key_count,
      activeCount: stat.active_count,
      avgUsage: stat.avg_usage,
    }))
  }
  catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const curdRef = ref<InstanceType<typeof StdCurd>>()

// StdCurd 配置
const config: CurdConfig<ApiKey> = {
  api: keyApi, // 临时使用 any 类型避免类型冲突
  title: 'API Key',

  // 表单字段配置
  formFields: [
    {
      key: 'user_id',
      label: '关联用户',
      type: 'combobox',
      required: true,
      placeholder: '请输入用户名或邮箱搜索',
      remoteSearch: {
        searchApi: searchUsers,
        debounceMs: 300,
        minSearchLength: 1,
        showDefaultOptions: false,
        renderOption: (option) => {
          const parts = [option.label]
          if (option.email) {
            parts.push(`(${option.email})`)
          }
          return parts.join(' ')
        },
      },
    },
    {
      key: 'name',
      label: 'Key名称',
      type: 'text',
      placeholder: '请输入Key名称',
      required: true,
    },
    {
      key: 'api_key',
      label: 'API Key',
      type: 'text',
      show: false,
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      defaultValue: 'ok',
      options: FORM_OPTIONS.API_KEY_STATUS,
      required: true,
    },
    {
      key: 'comment',
      label: '备注',
      type: 'textarea',
      placeholder: '请输入Key备注信息',
    },
  ],

  // 基础配置
  primaryKey: 'id',
  pageSize: PAGINATION.DEFAULT_PAGE_SIZE,

  // 功能开关
  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 消息配置
  confirmMessages: {
    delete: CONFIRM_MESSAGES.DELETE_API_KEY,
    batchDelete: CONFIRM_MESSAGES.BATCH_DELETE_API_KEYS,
  },

  successMessages: {
    create: SUCCESS_MESSAGES.API_KEY_CREATED,
    update: SUCCESS_MESSAGES.API_KEY_UPDATED,
    delete: SUCCESS_MESSAGES.API_KEY_DELETED,
    batchDelete: SUCCESS_MESSAGES.API_KEY_BATCH_DELETED,
  },
}

// 模块统计数据
const moduleStats = ref<Array<{
  module: string
  name: string
  keyCount: number
  activeCount: number
  avgUsage: number
}>>([])

// 查看Key详情
function viewKey(row: ApiKey) {
  router.push(`/billing/keys/${row.api_key}`)
}

function toggleKeyStatus(row: ApiKey) {
  keyApi.updateItem(row.api_key, {
    status: row.status === API_KEY_STATUS.OK ? API_KEY_STATUS.BLOCKED : API_KEY_STATUS.OK,
  }).then(() => {
    curdRef.value?.refresh()
  }).catch((error) => {
    toast.error(error.message || '更新Key状态失败')
  })
}

// 初始化
onMounted(() => {
  fetchStats()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          API Key 管理
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          管理系统中的所有API Key，包括查看、创建、禁用等操作。支持资源包和余额两种计费方式。
        </p>
      </div>
    </div>

    <!-- 计费方式说明 -->
    <Alert>
      <Info class="w-4 h-4" />
      <AlertDescription>
        <strong>计费说明：</strong>{{ BILLING_DESCRIPTIONS.API_KEY_SYSTEM }}
      </AlertDescription>
    </Alert>

    <!-- 标签页导航 -->
    <Tabs
      v-model="activeTab"
      class="w-full"
    >
      <TabsList class="grid w-full grid-cols-2 mb-2">
        <TabsTrigger value="overview">
          概览
        </TabsTrigger>
        <TabsTrigger value="list">
          API Key列表
        </TabsTrigger>
      </TabsList>

      <!-- 概览标签页 -->
      <TabsContent
        value="overview"
        class="space-y-6"
      >
        <!-- 核心统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总Key数量
              </CardTitle>
              <Key class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.totalKeys }}
              </div>
              <p class="text-xs text-muted-foreground">
                活跃: {{ stats.activeKeys }}，阻止: {{ stats.blockedKeys }}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                配额覆盖率
              </CardTitle>
              <Package class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ Math.round((stats.keysWithQuota / stats.totalKeys) * 100) }}%
              </div>
              <p class="text-xs text-muted-foreground">
                {{ stats.keysWithQuota }}/{{ stats.totalKeys }} 个Key有配额
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                平均使用率
              </CardTitle>
              <Activity class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.avgQuotaUsage.toFixed(2) }}%
              </div>
              <p class="text-xs text-muted-foreground">
                配额平均使用率: {{ stats.totalQuotaUsage.toFixed(2) }}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                余额覆盖率
              </CardTitle>
              <Wallet class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ Math.round((stats.keysWithBalance / stats.totalKeys) * 100).toFixed(2) }}%
              </div>
              <p class="text-xs text-muted-foreground">
                {{ stats.keysWithBalance }}/{{ stats.totalKeys }} 个Key用户有余额
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 按服务类型统计 -->
        <Card>
          <CardHeader>
            <CardTitle>按服务类型统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="module in moduleStats"
                :key="module.module"
                class="flex items-center justify-between p-4 border rounded-lg"
              >
                <div class="flex items-center gap-3">
                  <Badge variant="outline">
                    {{ module.name }}
                  </Badge>
                  <div class="text-sm text-gray-600">
                    {{ module.keyCount }} 个Key使用
                  </div>
                </div>
                <div class="text-right space-y-1">
                  <div class="font-medium">
                    活跃: {{ module.activeCount }}/{{ module.keyCount }}
                  </div>
                  <div class="text-sm text-gray-500">
                    平均使用率: {{ module.avgUsage.toFixed(2) }}%
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- API Key列表标签页 -->
      <TabsContent
        value="list"
        class="space-y-6"
      >
        <!-- 使用 StdCurd 组件 -->
        <StdCurd
          ref="curdRef"
          :config="config"
        >
          <!-- 自定义API Key列渲染 -->
          <template #cell-api_key="{ value, row }">
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                <Key class="w-5 h-5 text-gray-400" />
              </div>
              <div>
                <div class="flex items-center justify-between font-mono text-sm font-medium text-gray-900">
                  {{ maskApiKey(value) }}
                  <CheckCheck
                    v-if="isCopied(value)"
                    class="w-4 h-4 ml-2 text-success cursor-pointer"
                    @click="copyApiKey(value)"
                  />
                  <Copy
                    v-else
                    class="w-4 h-4 ml-2 cursor-pointer"
                    @click="copyApiKey(value)"
                  />
                </div>
                <div
                  v-if="row.name"
                  class="text-xs text-gray-500"
                >
                  {{ row.name }}
                </div>
              </div>
            </div>
          </template>

          <!-- 自定义关联用户列渲染 -->
          <template #cell-user_id="{ row }">
            <div
              v-if="row.user"
              class="flex items-center"
            >
              <Avatar class="h-8 w-8 mr-3">
                <AvatarImage :src="row.user.avatar" />
                <AvatarFallback>{{ (row.user.name || '').charAt(0).toUpperCase() }}</AvatarFallback>
              </Avatar>
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ row.user.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ row.user.email }}
                </div>
                <div
                  v-if="row.user.balance"
                  class="text-xs text-green-600"
                >
                  余额: {{ formatCurrency(row.user.balance) }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="text-sm text-gray-400"
            >
              无关联用户
            </div>
          </template>

          <!-- 自定义状态列渲染 -->
          <template #cell-status="{ value }">
            <Badge :variant="value === API_KEY_STATUS.OK ? 'default' : 'destructive'">
              <div class="flex items-center gap-1">
                <div
                  class="w-2 h-2 rounded-full"
                  :class="value === API_KEY_STATUS.OK ? 'bg-green-500' : 'bg-red-500'"
                />
                {{ value === API_KEY_STATUS.OK ? '正常' : '阻止' }}
              </div>
            </Badge>
          </template>

          <!-- 自定义创建时间列渲染 -->
          <template #cell-createdAt="{ value }">
            <div class="text-sm text-gray-500">
              {{ formatDate(value) }}
            </div>
          </template>

          <!-- 自定义操作列 -->
          <template #actions="{ row }">
            <div class="flex items-center justify-end gap-1">
              <Button
                variant="ghost"
                size="sm"
                @click="viewKey(row)"
              >
                <Eye class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="toggleKeyStatus(row)"
              >
                <component
                  :is="row.status === API_KEY_STATUS.OK ? ShieldCheck : ShieldX"
                  class="w-4 h-4"
                />
              </Button>
            </div>
          </template>
        </StdCurd>
      </TabsContent>
    </Tabs>
  </div>
</template>
