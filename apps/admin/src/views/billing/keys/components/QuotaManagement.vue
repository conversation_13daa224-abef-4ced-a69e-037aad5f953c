<script setup lang="ts">
import type { KeyStatusResponse } from '@/api/key'
import type { QuotaPackage } from '@/api/quota'
import {
  Badge,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Progress,
  Switch,
} from '@billing/ui'
import { BarChart3, CreditCard, Gift, Package, Plus, Shield } from 'lucide-vue-next'
import { MODULE_NAMES, QUOTA_STATUS } from '@/constants'
import {
  formatExpiryTime,
  getUsageRate,
  isQuotaExpired,
} from '@/utils'

interface Props {
  keyInfo: KeyStatusResponse
}

interface Emits {
  (e: 'addQuota'): void
  (e: 'toggleQuotaStatus', quota: QuotaPackage): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取配额来源信息
function getQuotaSource(quota: QuotaPackage) {
  const sources = {
    admin: { label: '管理员创建', icon: Shield, color: 'destructive' as const },
    purchase: { label: '用户购买', icon: CreditCard, color: 'default' as const },
    gift: { label: '系统赠送', icon: Gift, color: 'secondary' as const },
    promotion: { label: '活动赠送', icon: Gift, color: 'secondary' as const },
  }
  return sources[quota.type as keyof typeof sources] || sources.admin
}

function handleAddQuota() {
  emit('addQuota')
}

function handleToggleQuotaStatus(quota: QuotaPackage) {
  emit('toggleQuotaStatus', quota)
}
</script>

<template>
  <Card>
    <CardHeader>
      <div class="flex items-center justify-between">
        <CardTitle class="flex items-center gap-2">
          <BarChart3 class="w-5 h-5" />
          资源包管理
        </CardTitle>
        <Button
          size="sm"
          @click="handleAddQuota"
        >
          <Plus class="w-4 h-4" />
          添加资源包
        </Button>
      </div>
    </CardHeader>
    <CardContent>
      <div class="space-y-4">
        <div
          v-if="keyInfo.quotas.length === 0"
          class="text-center py-8 text-gray-500"
        >
          <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>暂无资源包配置</p>
          <p class="text-sm">
            点击上方"添加资源包"按钮为此Key配置资源包
          </p>
          <p class="text-sm text-blue-600 mt-2">
            支持为不同模型配置独立的资源包
          </p>
        </div>
        <div
          v-else
          class="grid gap-4"
        >
          <div
            v-for="quota in keyInfo.quotas"
            :key="quota.id"
            class="border rounded-lg p-4"
            :class="{ 'opacity-50': quota.status !== QUOTA_STATUS.ACTIVE || isQuotaExpired(quota.expires_at) || quota.available === 0 }"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2 flex-wrap">
                <Badge variant="outline">
                  {{ MODULE_NAMES[quota.module] }}
                </Badge>
                <Badge
                  v-if="quota.model_name"
                  variant="secondary"
                  class="text-xs"
                >
                  {{ quota.model_name }}
                </Badge>
                <Badge
                  v-else
                  variant="outline"
                  class="text-xs"
                >
                  通用
                </Badge>
                <!-- 配额来源 -->
                <Badge
                  :variant="getQuotaSource(quota).color"
                  class="text-xs"
                >
                  <component
                    :is="getQuotaSource(quota).icon"
                    class="w-3 h-3"
                  />
                  {{ getQuotaSource(quota).label }}
                </Badge>
                <!-- 状态标识 -->
                <Badge
                  v-if="isQuotaExpired(quota.expires_at)"
                  variant="destructive"
                  class="text-xs"
                >
                  已过期
                </Badge>
                <Badge
                  v-else-if="quota.status !== QUOTA_STATUS.ACTIVE"
                  variant="secondary"
                  class="text-xs"
                >
                  已禁用
                </Badge>
              </div>
              <!-- 启用/禁用开关 -->
              <div class="flex items-center gap-2">
                <span class="text-xs text-gray-500">
                  {{ quota.status === QUOTA_STATUS.ACTIVE ? '启用' : '禁用' }}
                </span>
                <Switch
                  :model-value="quota.status === QUOTA_STATUS.ACTIVE"
                  :disabled="isQuotaExpired(quota.expires_at) || quota.available === 0"
                  @update:model-value="handleToggleQuotaStatus(quota)"
                />
              </div>
            </div>

            <!-- 配额详情信息 -->
            <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
              <div>
                <span class="text-gray-500">过期时间:</span>
                <span
                  class="ml-2"
                  :class="{ 'text-red-600': isQuotaExpired(quota.expires_at) }"
                >
                  {{ formatExpiryTime(quota.expires_at) }}
                </span>
              </div>
              <div v-if="quota.operator">
                <span class="text-gray-500">操作人:</span>
                <span class="ml-2">{{ quota.operator.name }}</span>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>已用量 / 总配额</span>
                <span>{{ quota.used.toLocaleString() }} / {{ quota.quota.toLocaleString() }}</span>
              </div>
              <Progress
                :model-value="Number(getUsageRate(quota.used, quota.quota))"
                class="h-2"
              />
              <div class="flex justify-between text-xs text-gray-500">
                <span>使用率: {{ getUsageRate(quota.used, quota.quota) }}%</span>
                <span>剩余: {{ quota.available.toLocaleString() }}</span>
              </div>
            </div>

            <!-- 备注信息 -->
            <div
              v-if="quota.description"
              class="mt-3 p-2 bg-gray-50 rounded text-sm text-gray-600"
            >
              <div class="text-gray-500 text-xs mb-1">
                备注:
              </div>
              {{ quota.description }}
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
