<script setup lang="ts">
import type { <PERSON><PERSON><PERSON><PERSON>, User } from '@/api'
import {
  Card,
  CardContent,
  CardHeader,
  Skeleton,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@billing/ui'
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { keyApi, userApi } from '@/api'
import {
  UserApiKeys,
  UserBasicInfo,
  UserDangerZone,
  UserDeleteDialog,
  UserPasswordDialog,
  UserQuickActions,
  UserRechargeDialog,
  UserStats,
  UserStatusCard,
} from './components'

const route = useRoute()
const router = useRouter()

// 响应式数据
const user = ref<User | null>(null)
const loading = ref(false)
const showDeleteDialog = ref(false)
const showGeneratePasswordDialog = ref(false)
const showRechargeDialog = ref(false)
const userKeys = ref<ApiKey[]>([])
const keysLoading = ref(false)
const userStats = ref<{
  apiKeyCount: number
  totalUsage: number
  totalCost: number
  rechargeCount: number
  totalRecharge: number
} | null>(null)
const statsLoading = ref(false)

// 组件引用
const basicInfoRef = ref<InstanceType<typeof UserBasicInfo>>()

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchUserDetail()
    fetchUserKeys()
    fetchUserStats()
  }
}, { immediate: true })

// 获取用户详情
async function fetchUserDetail() {
  const userId = route.params.id as string
  if (!userId)
    return

  loading.value = true
  try {
    const response = await userApi.getItem(userId)
    user.value = response
  }
  catch (error: any) {
    console.error('获取用户详情失败:', error)
    toast.error('获取用户详情失败', {
      description: error?.message || '请稍后重试',
    })
  }
  finally {
    loading.value = false
  }
}

// 获取用户关联的API Key
async function fetchUserKeys() {
  const userId = route.params.id as string
  if (!userId)
    return

  keysLoading.value = true
  try {
    const response = await keyApi.getList({ user_id: userId })
    userKeys.value = response.data || []
  }
  catch (error: any) {
    console.error('获取用户API Key失败:', error)
    toast.error('获取API Key失败', {
      description: error?.message || '请稍后重试',
    })
  }
  finally {
    keysLoading.value = false
  }
}

// 获取用户统计信息
async function fetchUserStats() {
  const userId = route.params.id as string
  if (!userId)
    return

  statsLoading.value = true
  try {
    const response = await userApi.getUserStats(userId)
    userStats.value = response
  }
  catch (error: any) {
    console.error('获取用户统计失败:', error)
    // 统计信息获取失败不显示错误，只在控制台记录
  }
  finally {
    statsLoading.value = false
  }
}

// 处理基本信息保存成功
function handleSaveSuccess() {
  fetchUserDetail()
}

// 处理充值成功
function handleRechargeSuccess() {
  fetchUserDetail()
  fetchUserStats()
}

// 显示生成密码确认对话框
function showGeneratePasswordConfirm() {
  showGeneratePasswordDialog.value = true
}

// 显示充值对话框
function showRechargeConfirm() {
  showRechargeDialog.value = true
}

// 生成随机密码
async function generateRandomPassword() {
  const userId = route.params.id as string

  try {
    const result = await userApi.generateAndResetPassword(userId)

    // 关闭确认对话框
    showGeneratePasswordDialog.value = false

    // 复制到剪贴板
    await navigator.clipboard.writeText(result.password)

    toast.success('密码重置成功', {
      description: `新密码已生成并复制到剪贴板`,
      action: {
        label: '查看密码',
        onClick: () => toast.info('新密码', { description: result.password }),
      },
    })
  }
  catch (error: any) {
    console.error('生成随机密码失败:', error)
    toast.error('生成密码失败', {
      description: error?.message || '请稍后重试',
    })
  }
}

// 显示删除确认对话框
function showDeleteConfirmDialog() {
  showDeleteDialog.value = true
}

// 删除用户
async function deleteUser() {
  const userId = route.params.id as string

  try {
    await userApi.deleteItem(userId)
    showDeleteDialog.value = false

    toast.success('用户删除成功', {
      description: '用户及相关数据已删除',
    })

    // 返回用户列表
    router.push('/users')
  }
  catch (error: any) {
    console.error('删除用户失败:', error)
    toast.error('删除失败', {
      description: error?.message || '请稍后重试',
    })
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchUserDetail()
  fetchUserKeys()
  fetchUserStats()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <!-- <Button variant="ghost" @click="goBack">
          <ArrowLeft class="w-4 h-4" />
          返回
        </Button> -->
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            用户详情
          </h1>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="space-y-6"
    >
      <Card>
        <CardHeader>
          <Skeleton class="h-6 w-24" />
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="flex items-center gap-6">
              <Skeleton class="h-24 w-24 rounded-full" />
              <div class="space-y-2">
                <Skeleton class="h-4 w-20" />
                <Skeleton class="h-10 w-32" />
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <Skeleton class="h-16" />
              <Skeleton class="h-16" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 主要内容 -->
    <div
      v-else
      class="grid grid-cols-1 lg:grid-cols-3 gap-6"
    >
      <!-- 左侧主要内容 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 基本信息 -->
        <UserBasicInfo
          ref="basicInfoRef"
          :user="user"
          @save-success="handleSaveSuccess"
        />

        <!-- API Keys 和统计信息标签页 -->
        <Tabs
          default-value="keys"
          class="w-full"
        >
          <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="keys">
              关联 API Keys ({{ userKeys.length }})
            </TabsTrigger>
            <TabsTrigger value="stats">
              使用统计
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="keys"
            class="space-y-4"
          >
            <UserApiKeys
              :user-keys="userKeys"
              :loading="keysLoading"
              @refresh="fetchUserKeys"
            />
          </TabsContent>

          <TabsContent
            value="stats"
            class="space-y-4"
          >
            <UserStats
              :user-stats="userStats"
              :loading="statsLoading"
            />
          </TabsContent>
        </Tabs>
      </div>

      <!-- 右侧边栏 -->
      <div class="space-y-6">
        <!-- 账户状态 -->
        <UserStatusCard :user="user" />

        <!-- 快速操作 -->
        <UserQuickActions
          @generate-password="showGeneratePasswordConfirm"
          @recharge="showRechargeConfirm"
        />

        <!-- 危险操作 -->
        <UserDangerZone @delete-user="showDeleteConfirmDialog" />
      </div>
    </div>

    <!-- 生成随机密码确认对话框 -->
    <UserPasswordDialog
      v-model:open="showGeneratePasswordDialog"
      @confirm="generateRandomPassword"
    />

    <!-- 充值对话框 -->
    <UserRechargeDialog
      v-model:open="showRechargeDialog"
      :user="user"
      @success="handleRechargeSuccess"
    />

    <!-- 删除确认对话框 -->
    <UserDeleteDialog
      v-model:open="showDeleteDialog"
      :user="user"
      @confirm="deleteUser"
    />
  </div>
</template>
