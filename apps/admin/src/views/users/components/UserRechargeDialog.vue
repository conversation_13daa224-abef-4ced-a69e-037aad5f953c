<script setup lang="ts">
import type { User } from '@/api'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@billing/ui'
import {
  CreditCard,
  Loader2,
} from 'lucide-vue-next'
import { computed, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { rechargeApi } from '@/api'
import { FORM_OPTIONS, RECHARGE_TYPE } from '@/constants'

interface Props {
  open: boolean
  user: User | null
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive({
  amount: '',
  type: RECHARGE_TYPE.ADMIN,
  description: '',
})

// 充值类型选项 - 使用公共常量
const rechargeTypes = FORM_OPTIONS.RECHARGE_TYPES

// 计算属性
const isFormValid = computed(() => {
  const amount = Number.parseFloat(formData.amount)
  return amount > 0 && amount <= 999999 && formData.type
})

// 监听对话框打开状态，重置表单
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    resetForm()
  }
})

// 重置表单
function resetForm() {
  formData.amount = ''
  formData.type = RECHARGE_TYPE.ADMIN
  formData.description = ''
}

// 关闭对话框
function handleClose() {
  emit('update:open', false)
}

// 处理充值
async function handleRecharge() {
  if (!props.user?.id || !isFormValid.value) {
    toast.error('表单验证失败', {
      description: '请检查输入信息',
    })
    return
  }

  loading.value = true

  try {
    const amount = Number.parseFloat(formData.amount)

    await rechargeApi.recharge({
      user_id: props.user.id,
      amount,
      type: formData.type,
      description: formData.description || undefined,
    })

    toast.success('充值成功', {
      description: `已为用户充值 ¥${amount.toFixed(2)}`,
    })

    // 关闭对话框并通知父组件
    handleClose()
    emit('success')
  }
  catch (error: any) {
    console.error('充值失败:', error)
    toast.error('充值失败', {
      description: error?.message || '请稍后重试',
    })
  }
  finally {
    loading.value = false
  }
}

// 格式化金额输入
function formatAmountInput(event: Event) {
  const target = event.target as HTMLInputElement
  let value = target.value.replace(/[^0-9.]/g, '')

  // 限制小数点位数
  const dotIndex = value.indexOf('.')
  if (dotIndex !== -1) {
    value = value.substring(0, dotIndex + 3)
  }

  formData.amount = value
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="handleClose"
  >
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <CreditCard class="w-5 h-5" />
          用户充值
        </DialogTitle>
        <DialogDescription>
          为用户 "{{ user?.name }}" 充值余额
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- 充值金额 -->
        <div class="space-y-2">
          <Label for="amount">充值金额 <span class="text-red-500">*</span></Label>
          <div class="relative">
            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
            <Input
              id="amount"
              v-model="formData.amount"
              placeholder="0.00"
              class="pl-8"
              @input="formatAmountInput"
            />
          </div>
          <p class="text-xs text-gray-500">
            请输入1-999,999之间的金额
          </p>
        </div>

        <!-- 充值类型 -->
        <div class="space-y-2">
          <Label for="type">充值类型 <span class="text-red-500">*</span></Label>
          <Select v-model="formData.type">
            <SelectTrigger>
              <SelectValue placeholder="请选择充值类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="type in rechargeTypes"
                :key="type.value"
                :value="type.value"
              >
                {{ type.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 充值说明 -->
        <div class="space-y-2">
          <Label for="description">充值说明</Label>
          <Textarea
            id="description"
            v-model="formData.description"
            placeholder="请输入充值说明（可选）"
            class="min-h-[80px]"
          />
        </div>

        <!-- 当前余额显示 -->
        <div
          v-if="user"
          class="p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-600">当前余额</span>
            <span class="font-medium text-gray-900">
              ¥{{ (user.balance || 0).toFixed(2) }}
            </span>
          </div>
          <div
            v-if="formData.amount && parseFloat(formData.amount) > 0"
            class="flex justify-between items-center text-sm mt-1"
          >
            <span class="text-gray-600">充值后余额</span>
            <span class="font-medium text-green-600">
              ¥{{ ((user.balance || 0) + parseFloat(formData.amount)).toFixed(2) }}
            </span>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          :disabled="loading"
          @click="handleClose"
        >
          取消
        </Button>
        <Button
          :disabled="!isFormValid || loading"
          @click="handleRecharge"
        >
          <Loader2
            v-if="loading"
            class="w-4 h-4 mr-2 animate-spin"
          />
          确认充值
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
