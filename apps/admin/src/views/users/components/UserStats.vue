<script setup lang="ts">
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import {
  CreditCard,
  Loader2,
} from 'lucide-vue-next'
import { formatCurrency } from '@/utils/format'

interface UserStats {
  apiKeyCount: number
  totalUsage: number
  totalCost: number
  rechargeCount: number
  totalRecharge: number
}

interface Props {
  userStats: UserStats | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <CreditCard class="w-5 h-5" />
        使用统计
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div
        v-if="loading || !userStats"
        class="text-center py-8 text-gray-500"
      >
        <Loader2 class="w-8 h-8 mx-auto mb-4 animate-spin" />
        <p>加载统计数据中...</p>
      </div>
      <div
        v-else
        class="grid grid-cols-2 md:grid-cols-3 gap-4"
      >
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-blue-600">
            {{ userStats.apiKeyCount }}
          </div>
          <div class="text-sm text-gray-500">
            API Keys
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-green-600">
            {{ formatCurrency(userStats.totalCost) }}
          </div>
          <div class="text-sm text-gray-500">
            总消费
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-purple-600">
            {{ formatCurrency(userStats.totalRecharge) }}
          </div>
          <div class="text-sm text-gray-500">
            总充值
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-orange-600">
            {{ userStats.totalUsage.toLocaleString() }}
          </div>
          <div class="text-sm text-gray-500">
            总使用量
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-indigo-600">
            {{ userStats.rechargeCount }}
          </div>
          <div class="text-sm text-gray-500">
            充值次数
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-pink-600">
            {{ userStats.totalRecharge > 0 ? formatCurrency(userStats.totalRecharge - userStats.totalCost) : formatCurrency(0) }}
          </div>
          <div class="text-sm text-gray-500">
            余额
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
