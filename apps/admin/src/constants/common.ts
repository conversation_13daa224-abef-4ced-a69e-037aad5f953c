/**
 * 通用常量定义
 */

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_ROWS_PER_PAGE: 100,
} as const

// 时间间隔（毫秒）
export const TIME_INTERVALS = {
  AUTO_REFRESH: 30000, // 30秒
  COPY_TOAST_DURATION: 5000, // 5秒
  DEBOUNCE_DELAY: 300, // 300毫秒
} as const

// 用户状态
export const USER_STATUS = {
  ACTIVE: 1,
  DISABLED: 0,
} as const

// API Key 状态
export const API_KEY_STATUS = {
  OK: 'ok',
  BLOCKED: 'blocked',
} as const

// 配额状态
export const QUOTA_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  EXHAUSTED: 'exhausted',
  DISABLED: 'disabled',
} as const

// 充值状态
export const RECHARGE_STATUS = {
  COMPLETED: 'completed',
  PENDING: 'pending',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const

// 充值类型
export const RECHARGE_TYPE = {
  ADMIN: 'admin',
  ALIPAY: 'alipay',
  WECHAT: 'wechat',
  BANK: 'bank',
} as const

// 配额类型
export const QUOTA_TYPE = {
  ADMIN: 'admin',
  PURCHASE: 'purchase',
  PROMOTION: 'promotion',
  GIFT: 'gift',
} as const

// 计费类型
export const BILLING_TYPE = {
  QUOTA: 'quota',
  BALANCE: 'balance',
} as const

// 计费单位
export const BILLING_UNIT = {
  TOKENS: 'tokens',
  CHARACTERS: 'characters',
  SECONDS: 'seconds',
} as const

// 服务模块
export const SERVICE_MODULE = {
  LLM: 'llm',
  TTS: 'tts',
  ASR: 'asr',
} as const

// 货币类型
export const CURRENCY = {
  CNY: 'CNY',
  USD: 'USD',
} as const

// 文件类型
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'txt'],
  ALLOWED_UPLOAD: 'jpg,png,pdf,doc,docx',
} as const

// 默认配额设置
export const DEFAULT_QUOTA = {
  LLM: 1000000,
  TTS: 100000,
  ASR: 100000,
} as const

// 系统设置默认值
export const SYSTEM_DEFAULTS = {
  SITE_NAME: '计费系统管理端',
  SITE_DESCRIPTION: '现代化的API计费管理系统',
  TIMEZONE: 'Asia/Shanghai',
  DATE_FORMAT: 'YYYY-MM-DD',
  MAX_FILE_SIZE: 10, // MB
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 30, // 分钟
  SESSION_TIMEOUT: 120, // 分钟
  PASSWORD_EXPIRY_DAYS: 90,
} as const

// 表单验证规则
export const VALIDATION_RULES = {
  EMAIL_PATTERN: /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/,
  PHONE_PATTERN: /^1[3-9]\d{9}$/,
  MIN_PASSWORD_LENGTH: 6,
  MAX_PASSWORD_LENGTH: 50,
  MIN_NAME_LENGTH: 2,
  MAX_NAME_LENGTH: 50,
} as const

// 颜色主题
export const THEME_COLORS = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
} as const

// Badge 变体
export const BADGE_VARIANTS = {
  DEFAULT: 'default',
  SECONDARY: 'secondary',
  DESTRUCTIVE: 'destructive',
  OUTLINE: 'outline',
} as const

// 消息类型
export const MESSAGE_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const
