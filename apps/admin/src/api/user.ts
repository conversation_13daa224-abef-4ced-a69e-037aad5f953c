import type { ModelBase } from './http'
import type { USER_STATUS } from '@/constants/common'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface User extends ModelBase {
  id: string
  name: string
  password?: string
  email: string
  avatar: string
  phone: string
  status: number
  balance: number // 用户余额
  lastActive: string
  avatar_id?: string
}

// 密码重置请求
export interface ResetPasswordRequest {
  password: string
}

// 用户更新请求
export interface UserUpdateRequest {
  name?: string
  email?: string
  phone?: string
  status?: keyof typeof USER_STATUS
  avatar_id?: string
  password?: string
}

export const userApi = extendCurdApi(useCurdApi<User>('/admin/users'), {
  // 重置用户密码
  resetPassword: async (userId: string, password: string): Promise<void> => {
    return await http.post(`/admin/users/${userId}/reset-password`, { password })
  },

  // 生成随机密码并重置
  generateAndResetPassword: async (userId: string): Promise<{ password: string }> => {
    return await http.post(`/admin/users/${userId}/generate-password`)
  },

  // 更新用户状态
  updateStatus: async (userId: string, status: number): Promise<User> => {
    return await http.patch(`/admin/users/${userId}/status`, { status })
  },

  // 更新用户头像
  updateAvatar: async (userId: string, avatarId: string): Promise<User> => {
    return await http.patch(`/admin/users/${userId}/avatar`, { avatar_id: avatarId })
  },

  // 获取用户详细统计信息
  getUserStats: async (userId: string): Promise<{
    apiKeyCount: number
    totalUsage: number
    totalCost: number
    rechargeCount: number
    totalRecharge: number
  }> => {
    return await http.get(`/admin/users/${userId}/stats`)
  },
})

/**
 * 搜索用户
 */
export async function searchUsers(query: string): Promise<{ label: string, value: number, avatar?: string, email?: string }[]> {
  try {
    const response = await http.get<Array<{
      id: number
      name: string
      email: string
      avatar?: string
    }>>('/admin/users/search', {
      params: { q: query, limit: 20 },
    })

    return response.map(user => ({
      label: user.name,
      value: user.id,
      email: user.email,
      avatar: user.avatar,
    }))
  }
  catch (error) {
    console.error('搜索用户失败:', error)
    return []
  }
}
