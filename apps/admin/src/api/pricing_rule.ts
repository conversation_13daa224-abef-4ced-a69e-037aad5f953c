import type { ModelBase } from './http'
import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>du<PERSON> } from './key'
import { useCurdApi } from '@uozi-admin/request'

export type Unit = 'tokens' | 'characters' | 'seconds'
export interface PricingRule extends ModelBase {
  module: Module
  model_name: string
  unit_price: number
  currency: Currency
  unit: Unit
  base_unit: number
  is_active: boolean
  priority: number
  description: string
}

export const pricingRuleApi = useCurdApi<PricingRule>('/admin/billing/pricing_rules')
