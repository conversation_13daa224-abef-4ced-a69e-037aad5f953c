import type { ModelBase } from './http'
import type { Module } from './key'
import type { Unit } from './pricing_rule'
import type { User } from './user'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export type QuotaType = 'admin' | 'purchase' | 'promotion' | 'gift'
export type QuotaStatus = 'active' | 'expired' | 'exhausted' | 'disabled'

// 配额信息
export interface QuotaPackage extends ModelBase {
  user_id: string
  user?: User
  api_key?: string
  module: Module
  model_name?: string
  quota: number
  used: number
  available: number
  expires_at?: number
  status: QuotaStatus
  type: QuotaType
  description?: string
  operator_id?: string
  operator?: User
}

// 创建资源包请求接口（与后端保持一致）
export interface CreateQuotaPackageRequest {
  user_id: string
  api_key?: string
  module: Module
  model_name?: string
  quota: number
  expires_at?: number
  type?: QuotaType
  description?: string
}

// 更新资源包请求接口（与后端保持一致）
export interface UpdateQuotaPackageRequest {
  quota?: number
  expires_at?: number
  status?: QuotaStatus
  description?: string
}

// 资源包概览统计
export interface QuotaPackageOverviewStats {
  total: number // 总资源包数量
  active: number // 活跃资源包数量
  expired: number // 过期资源包数量
  exhausted: number // 用完资源包数量
  disabled: number // 禁用资源包数量
  expiring_soon: number // 即将过期数量（7天内）
  total_cost: number // 总成本
  total_usage: number // 总使用量（仅用于计算成本）
  module_stats: QuotaPackageModuleStat[] // 按模块统计
}

// 资源包模块统计
export interface QuotaPackageModuleStat {
  module: Module // 模块名称
  name: string // 模块显示名称
  count: number // 资源包数量
  total_quota: number // 总配额
  total_used: number // 总使用量
  available: number // 可用量
  usage_rate: number // 使用率
  unit: Unit // 单位
  cost: number // 模块总成本
}

export const quotaApi = extendCurdApi(useCurdApi<QuotaPackage>('/admin/billing/quota_packages'), {
  // 创建资源包
  createItem: async (data: CreateQuotaPackageRequest) => {
    return await http.post('/admin/billing/quota_packages', data)
  },
  // 更新资源包
  updateItem: async (id: string, data: UpdateQuotaPackageRequest) => {
    return await http.post(`/admin/billing/quota_packages/${id}`, data)
  },
  // 禁用资源包
  disableItem: async (id: string) => {
    return await http.put(`/admin/billing/quota_packages/${id}/disable`)
  },
  // 启用资源包
  enableItem: async (id: string) => {
    return await http.put(`/admin/billing/quota_packages/${id}/enable`)
  },
  // 获取用户资源包列表
  getUserQuotaPackages: async (userId: string) => {
    return await http.get(`/admin/billing/quota_packages?user_id=${userId}`)
  },
  // 获取资源包概览统计
  getOverviewStats: async (): Promise<QuotaPackageOverviewStats> => {
    return await http.get('/admin/billing/quota_packages/overview/stats')
  },
})
