# CRUD 组件迁移指南

本文档说明如何将现有的 `StdCurdConfig` 配置迁移到新的 `CurdConfig` 字段驱动配置模式。

## 🔄 已完成迁移的文件

以下文件已经成功迁移到新的配置模式：

- ✅ `src/views/uploads/index.vue` - 文件管理
- ✅ `src/views/users/index.vue` - 用户管理  
- ✅ `src/views/billing/pricing/index.vue` - 价格规则管理

## 🚧 待迁移的文件

以下文件需要手动完成迁移：

- ⏳ `src/views/billing/recharge/index.vue` - 充值管理
- ⏳ `src/views/billing/keys/index.vue` - API Key 管理
- ⏳ `src/views/billing/quota/index.vue` - 资源包管理

## 📋 迁移步骤

### 1. 更新导入

```typescript
// 旧的导入
import type { StdCurdConfig } from '@billing/curd'

// 新的导入
import type { CurdConfig } from '@billing/curd'
```

### 2. 更新配置类型

```typescript
// 旧的配置
const config: StdCurdConfig<DataType> = {
  // ...
}

// 新的配置
const config: CurdConfig<DataType> = {
  // ...
}
```

### 3. 转换配置结构

#### 旧的配置结构
```typescript
const config: StdCurdConfig<User> = {
  api: userApi,
  title: '用户管理',
  
  // 分离的配置
  columns: [
    {
      accessorKey: 'name',
      header: '姓名',
      meta: {
        isFormField: true,
        fieldType: 'text',
        required: true,
      },
    },
  ],
  
  // 或者
  formFields: [
    {
      key: 'name',
      label: '姓名',
      type: 'text',
      required: true,
    },
  ],
  
  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: true,
    search: true,
  },
}
```

#### 新的配置结构
```typescript
const config: CurdConfig<User> = {
  api: userApi,
  title: '用户管理',
  
  // 统一的字段配置
  fields: [
    {
      key: 'name',
      label: '姓名',
      type: 'text',
      table: { show: true },
      form: { 
        show: true, 
        required: true,
        rules: [
          { required: true, message: '姓名是必填项' },
        ],
      },
      search: { show: true, placeholder: '请输入姓名搜索' },
    },
  ],
  
  actions: {
    add: true,
    edit: true,
    delete: true,
    batchDelete: true,
  },
}
```

### 4. 字段配置映射

| 旧配置 | 新配置 |
|--------|--------|
| `columns[].accessorKey` | `fields[].key` |
| `columns[].header` | `fields[].label` |
| `columns[].meta.fieldType` | `fields[].type` |
| `columns[].meta.isFormField: true` | `fields[].form.show: true` |
| `columns[].meta.required` | `fields[].form.required` |
| `columns[].meta.hideInTable: true` | `fields[].table.show: false` |
| `formFields[].key` | `fields[].key` |
| `formFields[].label` | `fields[].label` |
| `formFields[].type` | `fields[].type` |
| `formFields[].required` | `fields[].form.required` |
| `formFields[].options` | `fields[].options` |
| `features.create` | `actions.add` |
| `features.edit` | `actions.edit` |
| `features.delete` | `actions.delete` |
| `features.batchDelete` | `actions.batchDelete` |

### 5. 自定义插槽名称

插槽名称需要使用 kebab-case 格式：

```vue
<!-- 旧的插槽名称 -->
<template #cell-user_id="{ row }">
<template #cell-created_at="{ value }">

<!-- 新的插槽名称 -->
<template #cell-user-id="{ row }">
<template #cell-created-at="{ value }">
```

### 6. 字段类型映射

| 旧类型 | 新类型 |
|--------|--------|
| `'input'` | `'text'` |
| `'text'` | `'text'` |
| `'email'` | `'email'` |
| `'password'` | `'password'` |
| `'number'` | `'number'` |
| `'select'` | `'select'` |
| `'switch'` | `'switch'` |
| `'textarea'` | `'textarea'` |
| `'datetime'` | `'datetime'` |
| `'date'` | `'date'` |

## 🎯 迁移示例

### 完整的迁移示例

```typescript
// 旧配置
const oldConfig: StdCurdConfig<ApiKey> = {
  api: keyApi,
  title: 'API Key',
  formFields: [
    {
      key: 'name',
      label: '名称',
      type: 'text',
      required: true,
    },
    {
      key: 'user_id',
      label: '关联用户',
      type: 'select',
      required: true,
      options: userOptions,
    },
  ],
  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: false,
    search: true,
  },
}

// 新配置
const newConfig: CurdConfig<ApiKey> = {
  api: keyApi,
  title: 'API Key 管理',
  fields: [
    {
      key: 'id',
      label: 'ID',
      type: 'text',
      table: { show: true, width: 80 },
      form: { show: false },
      search: { show: false },
    },
    {
      key: 'name',
      label: '名称',
      type: 'text',
      table: { show: true },
      form: { 
        show: true, 
        required: true,
        rules: [
          { required: true, message: '名称是必填项' },
        ],
      },
      search: { show: true, placeholder: '请输入名称搜索' },
    },
    {
      key: 'user_id',
      label: '关联用户',
      type: 'select',
      options: userOptions,
      table: { show: true, slot: 'cell-user' },
      form: { 
        show: true, 
        required: true,
        rules: [
          { required: true, message: '请选择关联用户' },
        ],
      },
      search: { show: true, placeholder: '请选择用户' },
    },
    {
      key: 'created_at',
      label: '创建时间',
      type: 'datetime',
      table: { show: true },
      form: { show: false },
      search: { show: true, type: 'date-range' },
    },
  ],
  actions: {
    add: true,
    edit: true,
    delete: true,
    batchDelete: false,
  },
}
```

## 🔧 迁移工具

可以使用以下正则表达式来辅助迁移：

1. 替换导入：
   ```
   查找: import type { StdCurdConfig }
   替换: import type { CurdConfig }
   ```

2. 替换配置类型：
   ```
   查找: : StdCurdConfig<
   替换: : CurdConfig<
   ```

3. 替换功能配置：
   ```
   查找: features:
   替换: actions:
   ```

4. 替换创建功能：
   ```
   查找: create:
   替换: add:
   ```

## ⚠️ 注意事项

1. **字段配置优先级**：新的配置模式中，每个字段的 `table`、`form`、`search` 配置是独立的
2. **插槽名称**：确保自定义插槽名称使用 kebab-case 格式
3. **类型安全**：新配置提供更好的类型安全，但可能需要添加类型断言 `as any` 来处理选项数组
4. **默认行为**：新配置模式提供了更合理的默认值，减少了必需的配置项

## 🚀 迁移后的优势

1. **统一配置**：一份配置搞定表格、表单、搜索
2. **更好的类型安全**：完整的 TypeScript 支持
3. **简化的 API**：减少配置复杂度
4. **更好的可维护性**：字段配置集中管理
5. **更灵活的控制**：每个字段可以独立控制在不同场景下的显示
