#!/usr/bin/env node

/**
 * CRUD 配置迁移脚本
 * 用于将旧的 StdCurdConfig 配置转换为新的 CurdConfig 字段驱动配置
 */

const fs = require('fs')
const path = require('path')

// 需要迁移的文件列表
const filesToMigrate = [
  'src/views/billing/recharge/index.vue',
  'src/views/billing/keys/index.vue', 
  'src/views/billing/quota/index.vue',
]

// 基本的文本替换规则
const replacementRules = [
  // 1. 更新导入
  {
    pattern: /import type { StdCurdConfig }/g,
    replacement: 'import type { CurdConfig }'
  },
  
  // 2. 更新配置类型声明
  {
    pattern: /: StdCurdConfig</g,
    replacement: ': CurdConfig<'
  },
  
  // 3. 更新功能配置
  {
    pattern: /features:\s*{/g,
    replacement: 'actions: {'
  },
  
  // 4. 更新创建功能
  {
    pattern: /create:/g,
    replacement: 'add:'
  },
  
  // 5. 移除 search 功能（新模式中自动根据字段配置决定）
  {
    pattern: /,?\s*search:\s*(true|false),?/g,
    replacement: ''
  }
]

// 插槽名称转换规则（snake_case 转 kebab-case）
const slotNameRules = [
  {
    pattern: /#cell-([a-z]+)_([a-z]+)/g,
    replacement: '#cell-$1-$2'
  },
  {
    pattern: /#cell-([a-z]+)_([a-z]+)_([a-z]+)/g,
    replacement: '#cell-$1-$2-$3'
  }
]

function migrateFile(filePath) {
  console.log(`正在迁移文件: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    console.warn(`文件不存在: ${filePath}`)
    return
  }
  
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 应用基本替换规则
  replacementRules.forEach(rule => {
    const newContent = content.replace(rule.pattern, rule.replacement)
    if (newContent !== content) {
      content = newContent
      modified = true
      console.log(`  ✓ 应用规则: ${rule.pattern}`)
    }
  })
  
  // 应用插槽名称转换规则
  slotNameRules.forEach(rule => {
    const newContent = content.replace(rule.pattern, rule.replacement)
    if (newContent !== content) {
      content = newContent
      modified = true
      console.log(`  ✓ 转换插槽名称: ${rule.pattern}`)
    }
  })
  
  if (modified) {
    // 创建备份
    const backupPath = filePath + '.backup'
    fs.writeFileSync(backupPath, fs.readFileSync(filePath))
    console.log(`  ✓ 创建备份: ${backupPath}`)
    
    // 写入修改后的内容
    fs.writeFileSync(filePath, content)
    console.log(`  ✓ 文件已更新`)
  } else {
    console.log(`  - 无需修改`)
  }
}

function generateFieldConfigTemplate(filePath) {
  console.log(`\n为 ${filePath} 生成字段配置模板...`)
  
  const templatePath = filePath.replace('.vue', '.fields-template.js')
  
  const template = `
// ${path.basename(filePath)} 字段配置模板
// 请根据原有的 formFields 或 columns 配置手动转换

const fields = [
  {
    key: 'id',
    label: 'ID',
    type: 'text',
    table: { show: true, width: 80 },
    form: { show: false },
    search: { show: false },
  },
  // TODO: 添加其他字段配置
  // 参考格式:
  // {
  //   key: 'field_name',
  //   label: '字段标签',
  //   type: 'text', // text, select, number, switch, datetime 等
  //   options: [], // 仅 select 类型需要
  //   table: { 
  //     show: true, 
  //     slot: 'cell-field-name' // 如果需要自定义渲染
  //   },
  //   form: { 
  //     show: true, 
  //     required: true,
  //     rules: [
  //       { required: true, message: '字段是必填项' },
  //     ],
  //   },
  //   search: { 
  //     show: true, 
  //     placeholder: '请输入搜索内容',
  //     type: 'date-range' // 仅日期字段需要
  //   },
  // },
]

// 替换原有配置中的 formFields 或 columns 为:
// fields: fields,
`
  
  fs.writeFileSync(templatePath, template)
  console.log(`  ✓ 模板已生成: ${templatePath}`)
}

function main() {
  console.log('🚀 开始 CRUD 配置迁移...\n')
  
  filesToMigrate.forEach(file => {
    const fullPath = path.join(__dirname, file)
    migrateFile(fullPath)
    generateFieldConfigTemplate(fullPath)
    console.log('')
  })
  
  console.log('✅ 迁移完成！')
  console.log('\n📋 后续步骤:')
  console.log('1. 检查生成的 .backup 文件确认更改')
  console.log('2. 根据 .fields-template.js 文件手动完成字段配置转换')
  console.log('3. 更新自定义插槽名称为 kebab-case 格式')
  console.log('4. 测试功能是否正常')
  console.log('5. 删除备份文件和模板文件')
  console.log('\n📖 详细迁移指南请参考: MIGRATION_GUIDE.md')
}

if (require.main === module) {
  main()
}

module.exports = {
  migrateFile,
  generateFieldConfigTemplate,
  replacementRules,
  slotNameRules
}
