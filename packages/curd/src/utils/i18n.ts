/**
 * 国际化支持
 */

// 支持的语言类型
export type SupportedLocale = 'zh-CN' | 'zh-TW' | 'en-US' | 'ja-JP' | 'ko-KR'

// 翻译键类型
export interface TranslationKeys {
  // 通用操作
  'common.create': string
  'common.edit': string
  'common.delete': string
  'common.save': string
  'common.cancel': string
  'common.confirm': string
  'common.search': string
  'common.refresh': string
  'common.export': string
  'common.import': string
  'common.loading': string
  'common.noData': string
  'common.total': string
  'common.selected': string

  // 表单相关
  'form.required': string
  'form.invalid': string
  'form.submit': string
  'form.reset': string
  'form.validation.required': string
  'form.validation.email': string
  'form.validation.url': string
  'form.validation.number': string
  'form.validation.minLength': string
  'form.validation.maxLength': string

  // 表格相关
  'table.actions': string
  'table.noData': string
  'table.loading': string
  'table.sortAsc': string
  'table.sortDesc': string
  'table.filter': string

  // 分页相关
  'pagination.prev': string
  'pagination.next': string
  'pagination.first': string
  'pagination.last': string
  'pagination.pageSize': string
  'pagination.total': string
  'pagination.goto': string

  // 消息提示
  'message.createSuccess': string
  'message.updateSuccess': string
  'message.deleteSuccess': string
  'message.deleteConfirm': string
  'message.batchDeleteConfirm': string
  'message.operationSuccess': string
  'message.operationFailed': string
  'message.networkError': string
  'message.permissionDenied': string

  // 文件上传
  'upload.selectFile': string
  'upload.dragHere': string
  'upload.uploading': string
  'upload.uploadSuccess': string
  'upload.uploadFailed': string
  'upload.fileTooLarge': string
  'upload.fileTypeNotSupported': string
}

// 默认翻译
const translations: Record<SupportedLocale, TranslationKeys> = {
  'zh-CN': {
    'common.create': '新建',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.confirm': '确认',
    'common.search': '搜索',
    'common.refresh': '刷新',
    'common.export': '导出',
    'common.import': '导入',
    'common.loading': '加载中...',
    'common.noData': '暂无数据',
    'common.total': '共 {total} 条',
    'common.selected': '已选择 {count} 项',

    'form.required': '必填',
    'form.invalid': '格式不正确',
    'form.submit': '提交',
    'form.reset': '重置',
    'form.validation.required': '此字段为必填项',
    'form.validation.email': '请输入有效的邮箱地址',
    'form.validation.url': '请输入有效的URL',
    'form.validation.number': '请输入有效的数字',
    'form.validation.minLength': '最少输入 {min} 个字符',
    'form.validation.maxLength': '最多输入 {max} 个字符',

    'table.actions': '操作',
    'table.noData': '暂无数据',
    'table.loading': '数据加载中...',
    'table.sortAsc': '升序排列',
    'table.sortDesc': '降序排列',
    'table.filter': '筛选',

    'pagination.prev': '上一页',
    'pagination.next': '下一页',
    'pagination.first': '首页',
    'pagination.last': '末页',
    'pagination.pageSize': '每页条数',
    'pagination.total': '共 {total} 条记录',
    'pagination.goto': '跳转至',

    'message.createSuccess': '创建成功',
    'message.updateSuccess': '更新成功',
    'message.deleteSuccess': '删除成功',
    'message.deleteConfirm': '确定要删除这条记录吗？',
    'message.batchDeleteConfirm': '确定要删除选中的 {count} 条记录吗？',
    'message.operationSuccess': '操作成功',
    'message.operationFailed': '操作失败',
    'message.networkError': '网络连接失败',
    'message.permissionDenied': '权限不足',

    'upload.selectFile': '选择文件',
    'upload.dragHere': '拖拽文件到此处',
    'upload.uploading': '上传中...',
    'upload.uploadSuccess': '上传成功',
    'upload.uploadFailed': '上传失败',
    'upload.fileTooLarge': '文件大小超出限制',
    'upload.fileTypeNotSupported': '不支持的文件类型'
  },

  'en-US': {
    'common.create': 'Create',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.search': 'Search',
    'common.refresh': 'Refresh',
    'common.export': 'Export',
    'common.import': 'Import',
    'common.loading': 'Loading...',
    'common.noData': 'No Data',
    'common.total': 'Total {total} items',
    'common.selected': '{count} items selected',

    'form.required': 'Required',
    'form.invalid': 'Invalid format',
    'form.submit': 'Submit',
    'form.reset': 'Reset',
    'form.validation.required': 'This field is required',
    'form.validation.email': 'Please enter a valid email address',
    'form.validation.url': 'Please enter a valid URL',
    'form.validation.number': 'Please enter a valid number',
    'form.validation.minLength': 'Minimum {min} characters required',
    'form.validation.maxLength': 'Maximum {max} characters allowed',

    'table.actions': 'Actions',
    'table.noData': 'No Data',
    'table.loading': 'Loading data...',
    'table.sortAsc': 'Sort ascending',
    'table.sortDesc': 'Sort descending',
    'table.filter': 'Filter',

    'pagination.prev': 'Previous',
    'pagination.next': 'Next',
    'pagination.first': 'First',
    'pagination.last': 'Last',
    'pagination.pageSize': 'Page Size',
    'pagination.total': 'Total {total} records',
    'pagination.goto': 'Go to',

    'message.createSuccess': 'Created successfully',
    'message.updateSuccess': 'Updated successfully',
    'message.deleteSuccess': 'Deleted successfully',
    'message.deleteConfirm': 'Are you sure you want to delete this record?',
    'message.batchDeleteConfirm': 'Are you sure you want to delete {count} selected records?',
    'message.operationSuccess': 'Operation successful',
    'message.operationFailed': 'Operation failed',
    'message.networkError': 'Network connection failed',
    'message.permissionDenied': 'Permission denied',

    'upload.selectFile': 'Select File',
    'upload.dragHere': 'Drag files here',
    'upload.uploading': 'Uploading...',
    'upload.uploadSuccess': 'Upload successful',
    'upload.uploadFailed': 'Upload failed',
    'upload.fileTooLarge': 'File size exceeds limit',
    'upload.fileTypeNotSupported': 'File type not supported'
  },

  // 其他语言的翻译可以后续添加
  'zh-TW': {} as TranslationKeys,
  'ja-JP': {} as TranslationKeys,
  'ko-KR': {} as TranslationKeys
}

// 国际化管理器
export class I18nManager {
  private static instance: I18nManager
  private currentLocale: SupportedLocale = 'zh-CN'
  private customTranslations: Partial<Record<SupportedLocale, Partial<TranslationKeys>>> = {}

  static getInstance(): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager()
    }
    return I18nManager.instance
  }

  // 设置当前语言
  setLocale(locale: SupportedLocale) {
    this.currentLocale = locale
  }

  // 获取当前语言
  getLocale(): SupportedLocale {
    return this.currentLocale
  }

  // 添加自定义翻译
  addTranslations(locale: SupportedLocale, translations: Partial<TranslationKeys>) {
    if (!this.customTranslations[locale]) {
      this.customTranslations[locale] = {}
    }
    Object.assign(this.customTranslations[locale]!, translations)
  }

  // 翻译函数
  t(key: keyof TranslationKeys, params?: Record<string, string | number>): string {
    const customTranslation = this.customTranslations[this.currentLocale]?.[key]
    const defaultTranslation = translations[this.currentLocale]?.[key]
    
    let text = customTranslation || defaultTranslation || key

    // 参数替换
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        text = text.replace(new RegExp(`\\{${paramKey}\\}`, 'g'), String(value))
      })
    }

    return text
  }
}

// Hook 函数
export function useI18n() {
  const i18n = I18nManager.getInstance()
  
  return {
    t: i18n.t.bind(i18n),
    setLocale: i18n.setLocale.bind(i18n),
    getLocale: i18n.getLocale.bind(i18n),
    addTranslations: i18n.addTranslations.bind(i18n)
  }
}