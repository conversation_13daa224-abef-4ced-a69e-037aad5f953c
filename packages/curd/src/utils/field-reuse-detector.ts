import type { ColumnDef } from '@tanstack/vue-table'
import type { FormFieldConfig } from '../types'
import type { CurdFieldConfig } from '../types/config'
import { FieldConfigParser } from './field-config-parser'

/**
 * 字段复用检测器
 * 支持新的统一字段配置模式
 */
export class FieldReuseDetector<T extends Record<string, any> = any> {
  /**
   * 从统一字段配置生成表格列和表单字段
   * 这是新的推荐方式
   */
  parseUnifiedFields(fields: CurdFieldConfig<T>[]): {
    formFields: FormFieldConfig<T>[]
    columns: ColumnDef<T>[]
    searchFields: FormFieldConfig<T>[]
  } {
    const fieldsWithDefaults = FieldConfigParser.applyDefaults(fields)
    return {
      formFields: FieldConfigParser.parseFormFields(fieldsWithDefaults),
      columns: FieldConfigParser.parseTableColumns(fieldsWithDefaults),
      searchFields: FieldConfigParser.parseSearchFields(fieldsWithDefaults),
    }
  }
}

/**
 * 创建字段复用检测器实例
 */
export function createFieldReuseDetector<T extends Record<string, any> = any>(): FieldReuseDetector<T> {
  return new FieldReuseDetector<T>()
}
