/**
 * 测试工具和模拟数据生成器
 */

import type { CurdApi, FormFieldConfig } from '../types'

// 模拟 API 响应生成器
export class MockApiGenerator<T extends Record<string, any>> {
  private data: T[] = []
  private idCounter = 1

  constructor(private generateItem: () => T & { id?: number | string }) {}

  // 生成模拟数据
  generateData(count: number): T[] {
    this.data = Array.from({ length: count }, () => ({
      id: this.idCounter++,
      ...this.generateItem(),
    } as T))
    return this.data
  }

  // 获取单个项目（内部方法）
  private getItem(id: string | number): T {
    const item = this.data.find(item => item.id === id)
    if (!item)
      throw new Error('Item not found')
    return item
  }

  // 创建模拟 API
  createMockApi(): CurdApi<T> {
    return {
      getList: async (params = {}) => {
        const { page = 1, per_page = 20, sort_field, sort_order } = params

        let filteredData = [...this.data]

        // 简单搜索过滤
        Object.entries(params).forEach(([key, value]) => {
          if (key !== 'page' && key !== 'per_page' && key !== 'sort_field' && key !== 'sort_order' && value) {
            filteredData = filteredData.filter(item =>
              String(item[key as keyof T]).toLowerCase().includes(String(value).toLowerCase()),
            )
          }
        })

        // 排序
        if (sort_field) {
          filteredData.sort((a, b) => {
            const aVal = a[sort_field as keyof T]
            const bVal = b[sort_field as keyof T]
            const result = aVal > bVal ? 1 : aVal < bVal ? -1 : 0
            return sort_order === 'desc' ? -result : result
          })
        }

        // 分页
        const start = (page - 1) * per_page
        const end = start + per_page
        const paginatedData = filteredData.slice(start, end)

        return {
          data: paginatedData,
          pagination: {
            current_page: page,
            per_page,
            total_pages: Math.ceil(filteredData.length / per_page),
            total: filteredData.length,
          },
        }
      },

      getItem: async (id) => {
        return this.getItem(id)
      },

      createItem: async (data) => {
        const newItem = { id: this.idCounter++, ...data } as unknown as T
        this.data.push(newItem)
        return newItem
      },

      updateItem: async (id, data) => {
        const index = this.data.findIndex(item => item.id === id)
        if (index === -1)
          throw new Error('Item not found')

        this.data[index] = { ...this.data[index], ...data }
        return this.data[index]
      },

      deleteItem: async (id) => {
        const index = this.data.findIndex(item => item.id === id)
        if (index === -1)
          throw new Error('Item not found')

        const deletedItem = this.data[index]
        this.data.splice(index, 1)
        return deletedItem
      },

      restoreItem: async (id) => {
        // 模拟恢复操作
        return this.getItem(id)
      },
    }
  }
}

// 表单字段配置生成器
export class FormFieldGenerator {
  // 根据数据类型自动生成表单字段配置
  static generateFromData<T>(sampleData: T, options: {
    excludeFields?: (keyof T)[]
    fieldTypes?: Partial<Record<keyof T, string>>
    labels?: Partial<Record<keyof T, string>>
  } = {}): FormFieldConfig<T>[] {
    const { excludeFields = [], fieldTypes = {}, labels = {} } = options

    return Object.keys(sampleData as object)
      .filter(key => !excludeFields.includes(key as keyof T))
      .map((key) => {
        const value = (sampleData as any)[key]
        const fieldKey = key as keyof T

        return {
          key: fieldKey,
          label: labels[fieldKey as string] || this.formatLabel(key),
          type: fieldTypes[fieldKey as string] || this.inferFieldType(value),
          required: false,
        }
      })
  }

  private static formatLabel(key: string): string {
    return key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim()
  }

  private static inferFieldType(value: any): string {
    if (typeof value === 'boolean')
      return 'switch'
    if (typeof value === 'number')
      return 'number'
    if (value instanceof Date)
      return 'date'
    if (typeof value === 'string') {
      if (value.includes('@'))
        return 'email'
      if (value.startsWith('http'))
        return 'url'
      if (value.length > 100)
        return 'textarea'
    }
    return 'text'
  }
}

// 测试数据生成器
export class TestDataGenerator {
  private static names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  private static companies = ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '滴滴', '小米', '华为']
  private static cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉']

  static randomName(): string {
    return this.names[Math.floor(Math.random() * this.names.length)]
  }

  static randomCompany(): string {
    return this.companies[Math.floor(Math.random() * this.companies.length)]
  }

  static randomCity(): string {
    return this.cities[Math.floor(Math.random() * this.cities.length)]
  }

  static randomEmail(): string {
    const domains = ['gmail.com', '163.com', 'qq.com', 'sina.com']
    const username = Math.random().toString(36).substring(2, 8)
    const domain = domains[Math.floor(Math.random() * domains.length)]
    return `${username}@${domain}`
  }

  static randomPhone(): string {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139']
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
    const suffix = Math.random().toString().substring(2, 10)
    return prefix + suffix
  }

  static randomDate(start: Date = new Date(2020, 0, 1), end: Date = new Date()): Date {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  }

  static randomNumber(min: number = 0, max: number = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  static randomBoolean(): boolean {
    return Math.random() > 0.5
  }

  static randomStatus(): string {
    const statuses = ['active', 'inactive', 'pending', 'suspended']
    return statuses[Math.floor(Math.random() * statuses.length)]
  }
}

// 示例：用户数据生成器
export interface User {
  id: string
  name: string
  email: string
  phone: string
  company: string
  city: string
  status: string
  createdAt: Date
  isActive: boolean
  score: number
}

export function createUserMockApi() {
  const generator = new MockApiGenerator<User>(() => ({
    id: Math.random().toString(36).substring(2, 8),
    name: TestDataGenerator.randomName(),
    email: TestDataGenerator.randomEmail(),
    phone: TestDataGenerator.randomPhone(),
    company: TestDataGenerator.randomCompany(),
    city: TestDataGenerator.randomCity(),
    status: TestDataGenerator.randomStatus(),
    createdAt: TestDataGenerator.randomDate(),
    isActive: TestDataGenerator.randomBoolean(),
    score: TestDataGenerator.randomNumber(0, 100),
  }))

  generator.generateData(100) // 生成100条测试数据
  return generator.createMockApi()
}

// 用户表单字段配置
export const userFormFields: FormFieldConfig<User>[] = [
  { key: 'name', label: '姓名', type: 'text', required: true },
  { key: 'email', label: '邮箱', type: 'email', required: true },
  { key: 'phone', label: '电话', type: 'tel', required: true },
  { key: 'company', label: '公司', type: 'text' },
  { key: 'city', label: '城市', type: 'select', options: [
    { label: '北京', value: '北京' },
    { label: '上海', value: '上海' },
    { label: '广州', value: '广州' },
    { label: '深圳', value: '深圳' },
  ] },
  { key: 'status', label: '状态', type: 'select', options: [
    { label: '激活', value: 'active' },
    { label: '未激活', value: 'inactive' },
    { label: '待审核', value: 'pending' },
    { label: '已暂停', value: 'suspended' },
  ] },
  { key: 'isActive', label: '是否启用', type: 'switch' },
  { key: 'score', label: '评分', type: 'number', numberConfig: { min: 0, max: 100 } },
]
