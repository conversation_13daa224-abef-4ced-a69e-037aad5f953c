import type { ColumnDef } from '@tanstack/vue-table'
import type { CurdFieldConfig, CurdFormFieldConfig, SearchFieldConfig, TableFieldConfig } from '../types/config'
import type { FormFieldConfig } from '../types/form'

/**
 * 字段配置解析器
 * 根据统一的字段配置生成表格列、表单字段和搜索字段配置
 */
export class FieldConfigParser {
  /**
   * 从字段配置生成表格列配置
   */
  static parseTableColumns<T = any>(fields: CurdFieldConfig<T>[]): ColumnDef<T>[] {
    return fields
      .filter((field) => {
        // 如果 table 配置为 false，则不显示
        if (field.table === false)
          return false
        // 如果 table 配置为 true 或对象，则显示
        if (field.table === true || (field.table && typeof field.table === 'object'))
          return true
        // 默认显示
        return field.table !== false
      })
      .map((field) => {
        const tableConfig = field.table as TableFieldConfig | boolean | undefined
        const config: ColumnDef<T> = {
          id: field.key as string,
          accessorKey: field.key as string,
          header: field.label,
        }

        // 如果是对象配置，应用详细配置
        if (tableConfig && typeof tableConfig === 'object') {
          if (tableConfig.width) {
            config.size = typeof tableConfig.width === 'number' ? tableConfig.width : undefined
          }
          if (tableConfig.sortable !== undefined) {
            config.enableSorting = tableConfig.sortable
          }
          if (tableConfig.render) {
            config.cell = ({ getValue, row }) => tableConfig.render!(getValue(), row.original)
          }
        }

        return config
      })
  }

  /**
   * 从字段配置生成表单字段配置
   */
  static parseFormFields<T = any>(fields: CurdFieldConfig<T>[]): FormFieldConfig<T>[] {
    return fields
      .filter((field) => {
        // 如果 form 配置为 false，则不显示
        if (field.form === false)
          return false
        // 如果 form 配置为 true 或对象，则显示
        if (field.form === true || (field.form && typeof field.form === 'object'))
          return true
        // 默认显示（除了 id 等主键字段）
        return field.key !== 'id' && field.form !== false
      })
      .map((field) => {
        const formConfig = field.form as CurdFormFieldConfig | boolean | undefined
        const fieldConfig: FormFieldConfig<T> = {
          key: field.key,
          label: field.label,
          type: field.type,
          placeholder: field.placeholder,
          options: field.options,
        }

        // 如果是对象配置，应用详细配置
        if (formConfig && typeof formConfig === 'object') {
          if (formConfig.required !== undefined) {
            fieldConfig.required = formConfig.required
          }
          if (formConfig.default !== undefined) {
            fieldConfig.defaultValue = formConfig.default
          }
          if (formConfig.disabled !== undefined) {
            fieldConfig.disabled = formConfig.disabled
          }
          if (formConfig.rules) {
            fieldConfig.rules = formConfig.rules.map(rule => ({
              required: rule.required,
              message: rule.message,
              min: rule.min,
              max: rule.max,
              pattern: rule.pattern,
              validator: rule.validator,
            }))
          }
          if (formConfig.col) {
            fieldConfig.col = formConfig.col
          }
          if (formConfig.componentProps) {
            fieldConfig.componentProps = formConfig.componentProps
          }
        }

        return fieldConfig
      })
  }

  /**
   * 从字段配置生成搜索字段配置
   */
  static parseSearchFields<T = any>(fields: CurdFieldConfig<T>[]): FormFieldConfig<T>[] {
    return fields
      .filter((field) => {
        // 如果 search 配置为 false，则不显示
        if (field.search === false)
          return false
        // 如果 search 配置为 true 或对象，则显示
        if (field.search === true || (field.search && typeof field.search === 'object'))
          return true
        // 默认不显示搜索
        return false
      })
      .map((field) => {
        const searchConfig = field.search as SearchFieldConfig | boolean | undefined
        const fieldConfig: FormFieldConfig<T> = {
          key: field.key,
          label: field.label,
          type: field.type,
          placeholder: field.placeholder || `请输入${field.label}`,
          options: field.options,
        }

        // 如果是对象配置，应用详细配置
        if (searchConfig && typeof searchConfig === 'object') {
          if (searchConfig.type) {
            fieldConfig.type = searchConfig.type
          }
          if (searchConfig.placeholder) {
            fieldConfig.placeholder = searchConfig.placeholder
          }
          if (searchConfig.componentProps) {
            fieldConfig.componentProps = searchConfig.componentProps
          }
        }

        // 搜索字段通常不是必填的
        fieldConfig.required = false

        return fieldConfig
      })
  }

  /**
   * 解析完整的字段配置
   */
  static parseFields<T = any>(fields: CurdFieldConfig<T>[]) {
    return {
      tableColumns: this.parseTableColumns(fields),
      formFields: this.parseFormFields(fields),
      searchFields: this.parseSearchFields(fields),
    }
  }

  /**
   * 获取默认的字段配置
   */
  static getDefaultFieldConfig(type: string): Partial<CurdFieldConfig> {
    const defaults: Record<string, Partial<CurdFieldConfig>> = {
      text: {
        table: { show: true },
        form: { show: true },
        search: { show: false },
      },
      input: {
        table: { show: true },
        form: { show: true },
        search: { show: true },
      },
      select: {
        table: { show: true },
        form: { show: true },
        search: { show: true },
      },
      switch: {
        table: { show: true },
        form: { show: true },
        search: { show: false },
      },
      datetime: {
        table: { show: true },
        form: { show: false },
        search: { show: true, type: 'date-range' },
      },
      date: {
        table: { show: true },
        form: { show: true },
        search: { show: true, type: 'date-range' },
      },
      number: {
        table: { show: true },
        form: { show: true },
        search: { show: false },
      },
      textarea: {
        table: { show: false },
        form: { show: true },
        search: { show: false },
      },
    }

    return defaults[type] || {
      table: { show: true },
      form: { show: true },
      search: { show: false },
    }
  }

  /**
   * 应用默认配置到字段
   */
  static applyDefaults<T = any>(fields: CurdFieldConfig<T>[]): CurdFieldConfig<T>[] {
    return fields.map((field) => {
      const defaults = this.getDefaultFieldConfig(field.type)
      return {
        ...defaults,
        ...field,
        table: field.table !== undefined ? field.table : defaults.table,
        form: field.form !== undefined ? field.form : defaults.form,
        search: field.search !== undefined ? field.search : defaults.search,
      }
    })
  }
}

/**
 * 便捷函数：解析字段配置
 */
export function parseFieldConfig<T = any>(fields: CurdFieldConfig<T>[]) {
  const fieldsWithDefaults = FieldConfigParser.applyDefaults(fields)
  return FieldConfigParser.parseFields(fieldsWithDefaults)
}

/**
 * 便捷函数：从字段配置生成表格列
 */
export function fieldsToTableColumns<T = any>(fields: CurdFieldConfig<T>[]): ColumnDef<T>[] {
  const fieldsWithDefaults = FieldConfigParser.applyDefaults(fields)
  return FieldConfigParser.parseTableColumns(fieldsWithDefaults)
}

/**
 * 便捷函数：从字段配置生成表单字段
 */
export function fieldsToFormFields<T = any>(fields: CurdFieldConfig<T>[]): FormFieldConfig<T>[] {
  const fieldsWithDefaults = FieldConfigParser.applyDefaults(fields)
  return FieldConfigParser.parseFormFields(fieldsWithDefaults)
}

/**
 * 便捷函数：从字段配置生成搜索字段
 */
export function fieldsToSearchFields<T = any>(fields: CurdFieldConfig<T>[]): FormFieldConfig<T>[] {
  const fieldsWithDefaults = FieldConfigParser.applyDefaults(fields)
  return FieldConfigParser.parseSearchFields(fieldsWithDefaults)
}
