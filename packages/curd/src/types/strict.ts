/**
 * 严格类型定义 - 提升类型安全性
 */

// 严格的 API 响应类型
export interface StrictApiResponse<T> {
  data: T
  success: boolean
  message?: string
  code?: number
}

// 严格的列表响应类型
export interface StrictListResponse<T> extends StrictApiResponse<T[]> {
  pagination: {
    current_page: number
    per_page: number
    total_pages: number
    total: number
  }
}

// 严格的 CURD API 接口
export interface StrictCurdApi<T, CreateData = Partial<T>, UpdateData = Partial<T>> {
  getList: (params?: Record<string, unknown>) => Promise<StrictListResponse<T>>
  getItem: (id: string | number) => Promise<StrictApiResponse<T>>
  createItem: (data: CreateData) => Promise<StrictApiResponse<T>>
  updateItem: (id: string | number, data: UpdateData) => Promise<StrictApiResponse<T>>
  deleteItem: (id: string | number, query?: Record<string, unknown>) => Promise<StrictApiResponse<T>>
  restoreItem?: (id: string | number) => Promise<StrictApiResponse<T>>
}

// 运行时类型验证器
export interface TypeValidator<T> {
  validate: (data: unknown) => data is T
  parse: (data: unknown) => T
  safeParse: (data: unknown) => { success: true; data: T } | { success: false; error: string }
}

// 字段值类型映射
export type FieldValueType<T extends string> = 
  T extends 'text' | 'email' | 'password' | 'url' | 'tel' | 'search' | 'textarea' ? string :
  T extends 'number' | 'number-field' | 'slider' | 'range-slider' ? number :
  T extends 'checkbox' | 'switch' | 'toggle' ? boolean :
  T extends 'select' | 'radio' ? string | number :
  T extends 'multiselect' | 'checkbox-group' | 'tags-input' ? (string | number)[] :
  T extends 'date' | 'datetime' | 'time' ? Date | string :
  T extends 'date-range' ? [Date | string, Date | string] :
  T extends 'file' | 'image' | 'avatar' ? File | string :
  T extends 'file-multiple' | 'image-multiple' ? (File | string)[] :
  T extends 'json' ? Record<string, unknown> :
  T extends 'color' ? string :
  T extends 'rating' ? number :
  unknown

// 严格的表单字段配置
export interface StrictFormFieldConfig<T, K extends keyof T = keyof T> {
  key: K
  label: string
  type: string
  required?: boolean
  defaultValue?: T[K]
  validator?: TypeValidator<T[K]>
  // 其他配置...
}