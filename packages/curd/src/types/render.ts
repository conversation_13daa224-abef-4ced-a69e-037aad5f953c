/**
 * 自定义渲染插槽说明
 *
 * CurdTable 和 EasyCurd 组件支持以下自定义插槽：
 *
 * 1. 表头自定义渲染：
 *    - 插槽名格式：`header-{columnId}`
 *    - 插槽参数：{ header, column, context }
 *    - 示例：`<template #header-name="{ header, column }">自定义表头</template>`
 *
 * 2. 单元格自定义渲染：
 *    - 插槽名格式：`cell-{columnId}`
 *    - 插槽参数：{ cell, row, value, index, column, context }
 *    - 示例：`<template #cell-status="{ value, row }">{{ value ? '启用' : '禁用' }}</template>`
 *
 * 3. 操作列自定义渲染：
 *    - 插槽名：`actions`
 *    - 插槽参数：{ row, index }
 *    - 示例：`<template #actions="{ row }">自定义操作按钮</template>`
 *
 * 4. 工具栏自定义渲染：
 *    - 插槽名：`toolbar-left` 或 `toolbar-right`
 *    - 示例：`<template #toolbar-left>自定义工具栏</template>`
 */

// 自定义渲染插槽类型定义
export interface CustomRenderSlots<T = any> {
  // 表头插槽参数
  [key: `header-${string}`]: {
    header: any
    column: any
    context: any
  }

  // 单元格插槽参数
  [key: `cell-${string}`]: {
    cell: any
    row: T
    value: any
    index: number
    column: any
    context: any
  }

  'before-actions'?: {
    row: T
    index: number
  }

  'after-actions'?: {
    row: T
    index: number
  }

  // 操作列插槽参数
  'actions'?: {
    row: T
    index: number
  }

  // 工具栏插槽
  'toolbar-left'?: Record<string, never>
  'toolbar-right'?: Record<string, never>
}