import type { ListParams, ListResponse } from './api'

// CURD 操作钩子
export interface CurdHooks<T = any> {
  // 列表相关
  beforeList?: (params: ListParams) => ListParams | Promise<ListParams> | false | Promise<false>
  afterList?: (response: ListResponse<T>) => void | Promise<void> | false | Promise<false>

  // 创建相关
  beforeCreate?: (data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false | Promise<false>
  afterCreate?: (data: T) => void | Promise<void> | false | Promise<false>

  // 更新相关
  beforeUpdate?: (id: string | number, data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false | Promise<false>
  afterUpdate?: (data: T) => void | Promise<void> | false | Promise<false>

  // 删除相关
  beforeDelete?: (id: string | number) => void | Promise<void> | false | Promise<false>
  afterDelete?: (id: string | number) => void | Promise<void> | false | Promise<false>

  // 批量删除相关
  beforeBatchDelete?: (ids: (string | number)[]) => void | Promise<void> | false | Promise<false>
  afterBatchDelete?: (ids: (string | number)[]) => void | Promise<void> | false | Promise<false>

  // 通用保存相关（create 和 update 的通用版本）
  beforeSave?: (data: Partial<T>, mode: 'create' | 'update', id?: string | number) => Partial<T> | Promise<Partial<T>> | false | Promise<false>
  afterSave?: (data: T, mode: 'create' | 'update') => void | Promise<void> | false | Promise<false>

  // 表单相关
  beforeFormSubmit?: (formData: Record<string, any>, mode: 'create' | 'update') => Record<string, any> | Promise<Record<string, any>> | false | Promise<false>
  afterFormSubmit?: (result: T, mode: 'create' | 'update') => void | Promise<void> | false | Promise<false>

  // 表单验证相关
  beforeValidate?: (formData: Record<string, any>) => Record<string, any> | Promise<Record<string, any>> | false | Promise<false>
  afterValidate?: (formData: Record<string, any>, isValid: boolean, errors?: any[]) => void | Promise<void> | false | Promise<false>

  // 选择相关
  beforeSelect?: (selectedIds: (string | number)[]) => void | Promise<void> | false | Promise<false>
  afterSelect?: (selectedIds: (string | number)[], selectedRows: T[]) => void | Promise<void> | false | Promise<false>

  // 搜索相关
  beforeSearch?: (searchParams: Record<string, any>) => Record<string, any> | Promise<Record<string, any>> | false | Promise<false>
  afterSearch?: (searchParams: Record<string, any>, results: T[]) => void | Promise<void> | false | Promise<false>

  // 排序相关
  beforeSort?: (field: string, order: 'asc' | 'desc') => { field: string, order: 'asc' | 'desc' } | Promise<{ field: string, order: 'asc' | 'desc' }> | false | Promise<false>
  afterSort?: (field: string, order: 'asc' | 'desc', results: T[]) => void | Promise<void> | false | Promise<false>

  // 刷新相关
  beforeRefresh?: () => void | Promise<void> | false | Promise<false>
  afterRefresh?: (results: T[]) => void | Promise<void> | false | Promise<false>

  // 错误处理
  onError?: (error: Error, operation: string, context?: any) => void | Promise<void> | false | Promise<false>
}