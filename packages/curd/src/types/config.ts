import type { Ref } from 'vue'
import type { CurdApi, ExtendedColumn, StdColumn } from './api'
import type { FormFieldConfig } from './form'
import type { CurdHooks } from './hooks'

// 按钮文本配置
export interface ButtonText {
  create?: string
  edit?: string
  delete?: string
  batchDelete?: string
  search?: string
  refresh?: string
  export?: string
  import?: string
}

// CURD 配置项
export interface CurdConfig<T = any> {
  api: CurdApi<T>
  columns: StdColumn<T>[]
  primaryKey?: string
  searchable?: boolean
  creatable?: boolean
  editable?: boolean
  deletable?: boolean
  batchDeletable?: boolean
  recyclable?: boolean
  pageSize?: number
  pageSizeOptions?: number[]
  btnText?: ButtonText
}

// 简化的 EasyCurd 配置
export interface StdCurdConfig<T = any> {
  api: CurdApi<T>
  title?: string

  // 字段配置：只需配置一种，另一种自动生成
  columns?: StdColumn<T>[]
  formFields?: FormFieldConfig<T>[]

  // 额外的表格列（不参与表单，只在表格中显示）
  extraColumns?: ExtendedColumn<T>[]

  // 全局列顺序配置（列的 key 或 id 数组）
  columnOrder?: string[]

  // 基础配置
  primaryKey?: string
  displayField?: string
  searchFields?: string[]
  sortFields?: string[]
  pageSize?: number
  pageSizeOptions?: number[]

  // 按钮文本配置
  btnText?: ButtonText

  // 功能开关
  features?: {
    create?: boolean
    edit?: boolean
    delete?: boolean
    batchDelete?: boolean
    search?: boolean
    export?: boolean
    import?: boolean
  }

  // 消息配置
  confirmMessages?: {
    delete?: string
    batchDelete?: string
  }

  successMessages?: {
    create?: string
    update?: string
    delete?: string
    batchDelete?: string
  }

  // hooks 钩子函数
  hooks?: CurdHooks<T>
}

export interface UseCurdOptions<T = any> {
  api: CurdApi<T>
  columns: StdColumn<T>[]
  pageSize?: number
  pageSizeOptions?: number[]
  searchable?: boolean
  creatable?: boolean
  editable?: boolean
  deletable?: boolean
  batchDeletable?: boolean
  hooks?: CurdHooks<T>
  immediate?: boolean
  primaryKey?: string
}

export interface UseCurdReturn<T = any> {
  data: Ref<T[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  total: Ref<number>
  currentPage: Ref<number>
  currentPageSize: Ref<number>
  totalPages: Ref<number>

  selectedRows: Ref<T[]>
  selectedRowKeys: Ref<(string | number)[]>

  searchParams: Ref<Record<string, any>>
  sortParams: Ref<Record<string, any>>

  refresh: () => Promise<void>
  search: (params: Record<string, any>) => Promise<void>
  sort: (field: string, order: 'asc' | 'desc') => Promise<void>
  changePage: (page: number) => Promise<void>
  changePageSize: (size: number) => Promise<void>
  create: (formData: Partial<T>) => Promise<T>
  update: (id: string | number, formData: Partial<T>) => Promise<T>
  remove: (id: string | number, permanently?: boolean) => Promise<void>
  batchRemove: (ids: (string | number)[], permanently?: boolean) => Promise<void>
  clearSelection: () => void
}
