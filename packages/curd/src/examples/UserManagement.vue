<template>
  <div class="user-management">
    <StdCurd :config="crudConfig" />
  </div>
</template>

<script setup lang="ts">
import type { CurdConfig } from '../types/config'
import { StdCurd } from '../index'

// 模拟用户数据类型
interface User {
  id: number
  username: string
  email: string
  role: 'admin' | 'user'
  status: boolean
  created_at: string
  last_login?: string
}

// 模拟API
const userApi = {
  async getList(params?: any) {
    // 模拟API调用
    console.log('获取用户列表', params)
    return {
      data: [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: true,
          created_at: '2024-01-01T00:00:00Z',
          last_login: '2024-01-15T10:30:00Z',
        },
        {
          id: 2,
          username: 'user1',
          email: '<EMAIL>',
          role: 'user',
          status: true,
          created_at: '2024-01-02T00:00:00Z',
          last_login: '2024-01-14T15:20:00Z',
        },
        {
          id: 3,
          username: 'user2',
          email: '<EMAIL>',
          role: 'user',
          status: false,
          created_at: '2024-01-03T00:00:00Z',
        },
      ] as User[],
      pagination: {
        current_page: 1,
        per_page: 20,
        total_pages: 1,
        total: 3,
      },
    }
  },

  async getItem(id: string | number) {
    console.log('获取用户详情', id)
    return {
      id: Number(id),
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      status: true,
      created_at: '2024-01-01T00:00:00Z',
    } as User
  },

  async createItem(data: Partial<User>) {
    console.log('创建用户', data)
    return {
      id: Date.now(),
      ...data,
      created_at: new Date().toISOString(),
    } as User
  },

  async updateItem(id: string | number, data: Partial<User>) {
    console.log('更新用户', id, data)
    return {
      id: Number(id),
      ...data,
    } as User
  },

  async deleteItem(id: string | number) {
    console.log('删除用户', id)
    return { id: Number(id) } as User
  },

  async restoreItem(id: string | number) {
    console.log('恢复用户', id)
    return { id: Number(id) } as User
  },
}

// 新的字段驱动配置
const crudConfig: CurdConfig<User> = {
  title: '用户管理',
  api: userApi,
  fields: [
    {
      key: 'id',
      label: 'ID',
      type: 'text',
      table: { show: true, width: 80 },
      form: { show: false },
      search: { show: false },
    },
    {
      key: 'username',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名',
      table: { show: true, sortable: true },
      form: { 
        show: true, 
        required: true,
        rules: [
          { required: true, message: '用户名不能为空' },
          { min: 3, message: '用户名至少3个字符' },
          { max: 20, message: '用户名最多20个字符' },
        ],
      },
      search: { 
        show: true, 
        placeholder: '请输入用户名搜索' 
      },
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入邮箱地址',
      table: { show: true, sortable: true },
      form: { 
        show: true, 
        required: true,
        rules: [
          { required: true, message: '邮箱不能为空' },
          { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入有效的邮箱地址' },
        ],
      },
      search: { 
        show: true, 
        placeholder: '请输入邮箱搜索' 
      },
    },
    {
      key: 'role',
      label: '角色',
      type: 'select',
      options: [
        { label: '管理员', value: 'admin' },
        { label: '普通用户', value: 'user' },
      ],
      table: { show: true, sortable: true },
      form: { 
        show: true, 
        required: true,
        default: 'user',
      },
      search: { 
        show: true,
        placeholder: '请选择角色' 
      },
    },
    {
      key: 'status',
      label: '状态',
      type: 'switch',
      table: { 
        show: true,
        render: (value: boolean) => value ? '启用' : '禁用'
      },
      form: { 
        show: true,
        default: true,
      },
      search: { 
        show: true,
        type: 'select',
        componentProps: {
          options: [
            { label: '启用', value: true },
            { label: '禁用', value: false },
          ],
        },
      },
    },
    {
      key: 'created_at',
      label: '创建时间',
      type: 'datetime',
      table: { 
        show: true, 
        sortable: true,
        render: (value: string) => value ? new Date(value).toLocaleString() : ''
      },
      form: { show: false },
      search: { 
        show: true,
        type: 'date-range',
        placeholder: '请选择创建时间范围'
      },
    },
    {
      key: 'last_login',
      label: '最后登录',
      type: 'datetime',
      table: { 
        show: true,
        render: (value: string) => value ? new Date(value).toLocaleString() : '从未登录'
      },
      form: { show: false },
      search: { show: false },
    },
  ],
  
  // 操作按钮配置
  actions: {
    add: true,
    edit: true,
    delete: true,
    batchDelete: true,
  },
  
  // 分页配置
  pagination: true,
  pageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],
  
  // 按钮文本配置
  btnText: {
    create: '新建用户',
    edit: '编辑',
    delete: '删除',
    batchDelete: '批量删除',
    search: '搜索',
    refresh: '刷新',
  },
  
  // 消息配置
  confirmMessages: {
    delete: '确定要删除这个用户吗？',
    batchDelete: '确定要删除选中的用户吗？',
  },
  
  successMessages: {
    create: '用户创建成功',
    update: '用户更新成功',
    delete: '用户删除成功',
    batchDelete: '批量删除成功',
  },
  
  // 钩子函数
  hooks: {
    beforeFormSubmit: async (formData, mode) => {
      console.log('表单提交前', formData, mode)
      // 可以在这里进行数据预处理
      if (mode === 'create') {
        // 创建时的特殊处理
        formData.created_at = new Date().toISOString()
      }
      return formData
    },
    
    afterFormSubmit: async (result, mode) => {
      console.log('表单提交后', result, mode)
      // 可以在这里进行后续处理，如发送通知等
      return true
    },
  },
}
</script>

<style scoped>
.user-management {
  padding: 20px;
}
</style>
