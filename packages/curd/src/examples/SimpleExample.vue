<template>
  <div class="simple-example">
    <h1>简单示例 - 产品管理</h1>
    <StdCurd :config="crudConfig" />
  </div>
</template>

<script setup lang="ts">
import type { CurdConfig } from '../types/config'
import { StdCurd } from '../index'

// 产品数据类型
interface Product {
  id: number
  name: string
  price: number
  category: string
  status: boolean
  created_at: string
}

// 模拟API
const productApi = {
  async getList() {
    return {
      data: [
        { id: 1, name: 'iPhone 15', price: 999, category: 'phone', status: true, created_at: '2024-01-01' },
        { id: 2, name: 'MacBook Pro', price: 1999, category: 'laptop', status: true, created_at: '2024-01-02' },
        { id: 3, name: 'iPad Air', price: 599, category: 'tablet', status: false, created_at: '2024-01-03' },
      ] as Product[],
      pagination: { current_page: 1, per_page: 20, total_pages: 1, total: 3 },
    }
  },
  async getItem(id: string | number) {
    return { id: Number(id), name: 'Product', price: 100, category: 'other', status: true, created_at: '2024-01-01' } as Product
  },
  async createItem(data: Partial<Product>) {
    return { id: Date.now(), ...data, created_at: new Date().toISOString() } as Product
  },
  async updateItem(id: string | number, data: Partial<Product>) {
    return { id: Number(id), ...data } as Product
  },
  async deleteItem(id: string | number) {
    return { id: Number(id) } as Product
  },
  async restoreItem(id: string | number) {
    return { id: Number(id) } as Product
  },
}

// 最简配置示例
const crudConfig: CurdConfig<Product> = {
  title: '产品管理',
  api: productApi,
  fields: [
    {
      key: 'id',
      label: 'ID',
      type: 'text',
      // 使用默认配置：table显示，form不显示，search不显示
    },
    {
      key: 'name',
      label: '产品名称',
      type: 'input',
      // 使用默认配置：table显示，form显示，search显示
    },
    {
      key: 'price',
      label: '价格',
      type: 'number',
      // 使用默认配置：table显示，form显示，search不显示
    },
    {
      key: 'category',
      label: '分类',
      type: 'select',
      options: [
        { label: '手机', value: 'phone' },
        { label: '笔记本', value: 'laptop' },
        { label: '平板', value: 'tablet' },
        { label: '其他', value: 'other' },
      ],
      // 使用默认配置：table显示，form显示，search显示
    },
    {
      key: 'status',
      label: '状态',
      type: 'switch',
      // 使用默认配置：table显示，form显示，search不显示
    },
    {
      key: 'created_at',
      label: '创建时间',
      type: 'date',
      // 使用默认配置：table显示，form不显示，search显示（日期范围）
    },
  ],
}
</script>

<style scoped>
.simple-example {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: #333;
}
</style>
