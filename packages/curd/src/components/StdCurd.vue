<script setup lang="ts">
import type { CurdConfig, FormFieldConfig, StdCurdConfig } from '../types'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@billing/ui'
import { computed, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import { useCurd } from '../composables/useCurd'
import { createFieldReuseDetector } from '../utils/field-reuse-detector'
import CurdComponent from './CurdComponent.vue'
import CurdForm from './StdForm.vue'
import CurdTable from './StdTable.vue'

interface Props {
  config: StdCurdConfig | CurdConfig
}

const props = defineProps<Props>()

// 检测是否为新的配置格式
const isNewConfig = computed(() => {
  return 'fields' in props.config && Array.isArray(props.config.fields)
})

// 如果是新配置格式，直接使用新组件
const newConfig = computed(() => {
  if (isNewConfig.value) {
    return props.config as CurdConfig
  }
  return null
})

// 推断列配置时传递模式和权限信息
const inferredColumns = computed(() => {
  const detector = createFieldReuseDetector()

  const result = detector.autoCompleteConfig({
    formFields: props.config.formFields,
    columns: props.config.columns,
    extraColumns: props.config.extraColumns,
    columnOrder: props.config.columnOrder,
  })

  // 根据权限过滤列
  return result.columns.filter((column) => {
    const meta = column.meta as any
    if (meta?.hideInTable) {
      return typeof meta.hideInTable === 'function'
        ? !meta.hideInTable({})
        : !meta.hideInTable
    }
    return true
  })
})

// 推断表单字段时考虑模式
const inferredFormFields = computed((): FormFieldConfig[] => {
  const detector = createFieldReuseDetector()

  const result = detector.autoCompleteConfig({
    formFields: props.config.formFields,
    columns: props.config.columns,
    extraColumns: props.config.extraColumns,
    columnOrder: props.config.columnOrder,
  })

  return result.formFields
})

// 使用 useCurd 组合式函数
const curdState = useCurd({
  api: props.config.api,
  columns: inferredColumns.value,
  pageSize: finalConfig.value.pageSize,
  pageSizeOptions: finalConfig.value.pageSizeOptions,
  searchable: finalConfig.value.features.search,
  creatable: finalConfig.value.features.create,
  editable: finalConfig.value.features.edit,
  deletable: finalConfig.value.features.delete,
  batchDeletable: finalConfig.value.features.batchDelete,
  hooks: finalConfig.value.hooks,
})

// 解构状态和方法
const {
  data,
  loading,
  error,
  total,
  currentPage,
  currentPageSize,
  totalPages,
  selectedRowKeys,
  selectedRows,
  searchParams,
  sortParams,
  refresh,
  search,
  sort,
  create,
  update,
  remove,
  batchRemove,
  clearSelection,
  changePage,
  changePageSize,
} = curdState

defineExpose({
  refresh,
})

// 表单相关状态
const formOpen = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const formTitle = computed(() =>
  formMode.value === 'create' ? `新建${finalConfig.value.title}` : `编辑${finalConfig.value.title}`,
)
const editingData = ref<any>(null)
const formLoading = ref(false)

// 确认对话框状态
const deleteConfirmOpen = ref(false)
const deletingId = ref<string | number | null>(null)
const batchDeleteConfirmOpen = ref(false)

// 处理创建
function handleCreate() {
  formMode.value = 'create'
  editingData.value = null
  formOpen.value = true
}

// 处理编辑
function handleEdit(row: any) {
  formMode.value = 'edit'
  editingData.value = { ...row }
  formOpen.value = true
}

// 处理表单提交
async function handleFormSubmit(formData: Record<string, any>) {
  try {
    formLoading.value = true

    // 执行 beforeFormSubmit 钩子
    if (finalConfig.value.hooks?.beforeFormSubmit) {
      const mode = formMode.value === 'edit' ? 'update' : formMode.value
      const hookResult = await finalConfig.value.hooks.beforeFormSubmit(formData, mode)
      if (hookResult === false) {
        showErrorMessage('表单提交被中断')
        return
      }
      // 如果钩子返回了新的表单数据，则使用新数据
      if (hookResult) {
        formData = hookResult
      }
    }

    let result: any
    if (formMode.value === 'create') {
      result = await create(formData)
      showSuccessMessage(finalConfig.value.successMessages.create)
    }
    else {
      const id = editingData.value[finalConfig.value.primaryKey]
      result = await update(id, formData)
      showSuccessMessage(finalConfig.value.successMessages.update)
    }

    // 执行 afterFormSubmit 钩子
    if (finalConfig.value.hooks?.afterFormSubmit) {
      const mode = formMode.value === 'edit' ? 'update' : formMode.value
      const hookResult = await finalConfig.value.hooks.afterFormSubmit(result, mode)
      if (hookResult === false) {
        console.warn('afterFormSubmit 钩子返回 false，但表单提交已完成')
      }
    }

    formOpen.value = false
    await refresh()
  }
  catch (error) {
    console.error('表单提交失败:', error)
    showErrorMessage('操作失败，请重试')
  }
  finally {
    formLoading.value = false
  }
}

// 处理删除确认
function handleDeleteConfirm(row: any) {
  deletingId.value = row[finalConfig.value.primaryKey]
  deleteConfirmOpen.value = true
}

// 执行删除
async function handleDelete() {
  if (deletingId.value === null)
    return

  try {
    await remove(deletingId.value)
    showSuccessMessage(finalConfig.value.successMessages.delete)
    deleteConfirmOpen.value = false
    await refresh()
  }
  catch (error) {
    console.error('删除失败:', error)
    showErrorMessage('删除失败，请重试')
  }
}

// 处理批量删除确认
function handleBatchDeleteConfirm() {
  if (selectedRowKeys.value.length === 0)
    return
  batchDeleteConfirmOpen.value = true
}

// 执行批量删除
async function handleBatchDelete() {
  try {
    await batchRemove(selectedRowKeys.value)
    showSuccessMessage(finalConfig.value.successMessages.batchDelete)
    batchDeleteConfirmOpen.value = false
    clearSelection()
    await refresh()
  }
  catch (error) {
    console.error('批量删除失败:', error)
    showErrorMessage('批量删除失败，请重试')
  }
}

// 显示成功消息
function showSuccessMessage(message: string) {
  // 这里可以集成你的消息提示组件
  toast.success(message)
}

// 显示错误消息
function showErrorMessage(message: string) {
  // 这里可以集成你的消息提示组件
  toast.error(message)
}

// 组件挂载时加载数据
onMounted(() => {
  refresh()
})
</script>

<template>
  <div class="easy-curd">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <h2 class="text-lg font-semibold">
          {{ finalConfig.title }}
        </h2>
      </div>
    </div>

    <!-- 表格 -->
    <CurdTable
      :api="props.config.api"
      :columns="inferredColumns"
      :data="data"
      :loading="loading"
      :error="error"
      :total="total"
      :current-page="currentPage"
      :current-page-size="currentPageSize"
      :total-pages="totalPages"
      :selected-row-keys="selectedRowKeys"
      :selected-rows="selectedRows"
      :row-key="finalConfig.primaryKey"
      :search-params="searchParams"
      :sort-params="sortParams"
      :primary-key="finalConfig.primaryKey"
      :searchable="finalConfig.features.search"
      :creatable="finalConfig.features.create"
      :editable="finalConfig.features.edit"
      :deletable="finalConfig.features.delete"
      :page-size-options="finalConfig.pageSizeOptions"
      :refresh="refresh"
      :search="search"
      :sort="sort"
      :clear-selection="clearSelection"
      :change-page="changePage"
      :change-page-size="changePageSize"
      :btn-text="finalConfig.btnText"
      @refresh="refresh"
      @click-create="handleCreate"
      @click-batch-delete="handleBatchDeleteConfirm"
      @click-edit="handleEdit"
      @click-delete="handleDeleteConfirm"
      @page-change="changePage"
      @page-size-change="changePageSize"
    >
      <!-- 透传所有自定义插槽 -->
      <template
        v-for="(_, slotName) in $slots"
        :key="slotName"
        #[slotName]="slotProps"
      >
        <slot
          :name="slotName"
          v-bind="slotProps || {}"
        />
      </template>
    </CurdTable>

    <!-- 表单对话框 -->
    <CurdForm
      :open="formOpen"
      :title="formTitle"
      :fields="inferredFormFields"
      :initial-data="editingData"
      :loading="formLoading"
      :mode="formMode"
      @update:open="formOpen = $event"
      @submit="handleFormSubmit"
      @cancel="formOpen = false"
    />

    <!-- 删除确认对话框 -->
    <AlertDialog
      :open="deleteConfirmOpen"
      @update:open="deleteConfirmOpen = $event"
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            {{ finalConfig.confirmMessages.delete }}
            此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive hover:bg-destructive/90"
            @click="handleDelete"
          >
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 批量删除确认对话框 -->
    <AlertDialog
      :open="batchDeleteConfirmOpen"
      @update:open="batchDeleteConfirmOpen = $event"
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认批量删除</AlertDialogTitle>
          <AlertDialogDescription>
            {{ finalConfig.confirmMessages.batchDelete }}
            将删除 {{ selectedRowKeys.length }} 条记录，此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive hover:bg-destructive/90"
            @click="handleBatchDelete"
          >
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<style scoped>
@reference "@billing/ui/globals.css";

.easy-curd {
  @apply w-full space-y-4;
}
</style>
