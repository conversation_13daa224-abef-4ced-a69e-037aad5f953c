<script setup lang="ts">
import type { FormFieldConfig, FormFieldOptions } from '../types'
import { computed } from 'vue'
import DateField from './fields/DateField.vue'
import FileField from './fields/FileField.vue'
import InputField from './fields/InputField.vue'
import NumberField from './fields/NumberField.vue'
import SelectField from './fields/SelectField.vue'
import SpecialField from './fields/SpecialField.vue'
import ToggleField from './fields/ToggleField.vue'

interface FieldState {
  loading: boolean
  error: string | null
  dynamicOptions: FormFieldOptions
  dynamicProps: Record<string, any>
}

interface Props {
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
  fieldOptions?: FormFieldOptions
  fieldState?: FieldState
  dynamicProps?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  fieldOptions: () => [],
  fieldState: () => ({
    loading: false,
    error: null,
    dynamicOptions: [],
    dynamicProps: {},
  }),
  dynamicProps: () => ({}),
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}

// 计算属性：字段是否可见
const isVisible = computed(() => {
  if (typeof props.field.show === 'function') {
    return props.field.show({})
  }
  return props.field.show !== false
})

// 获取字段类型组件
function getFieldComponent(type: string) {
  // 输入类字段
  if (['text', 'email', 'password', 'number', 'url', 'tel', 'search', 'textarea'].includes(type)) {
    return InputField
  }

  // 日期类字段
  if (['date', 'datetime', 'time'].includes(type)) {
    return DateField
  }

  // 选择类字段
  if (['select', 'combobox', 'radio-group', 'checkbox-group'].includes(type)) {
    return SelectField
  }

  // 开关类字段
  if (['checkbox', 'switch'].includes(type)) {
    return ToggleField
  }

  // 数字类字段
  if (['number-field', 'slider', 'progress'].includes(type)) {
    return NumberField
  }

  // 文件上传字段
  if (['file', 'image', 'file-multiple', 'image-multiple', 'avatar'].includes(type)) {
    return FileField
  }

  // 特殊字段
  if (['tags-input', 'pin-input', 'calendar', 'color', 'custom'].includes(type)) {
    return SpecialField
  }

  // 默认使用输入字段
  return InputField
}
</script>

<template>
  <component
    :is="getFieldComponent(field.type)"
    v-if="isVisible"
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    :field-options="fieldOptions"
    :field-state="fieldState"
    :dynamic-props="dynamicProps"
    @update:model-value="handleChange"
  />
</template>
